{"format_version": "1.8.0", "animations": {"misc.idle": {"loop": true, "animation_length": 1, "bones": {"head": {"rotation": {"0.75": {"vector": [0, 0, 2.5]}, "0.7917": {"vector": [0, 0, 2.03]}, "0.8333": {"vector": [0, 0, -2.72]}, "0.875": {"vector": [0, 0, -2.96]}, "0.9167": {"vector": [0, 0, -3.26]}, "0.9583": {"vector": [0, 0, 3.77]}, "1.0": {"vector": [0, 0, 0]}}}, "pupil_left": {"position": {"0.1667": {"vector": [0, 0, 0]}, "0.2083": {"vector": [0.70643, -0.03084, 0]}, "0.375": {"vector": [0.70643, -0.03084, 0]}, "0.4167": {"vector": [-0.70643, 0.03084, 0]}, "0.625": {"vector": [-0.70643, 0.03084, 0]}, "0.6667": {"vector": [0, 0, 0]}}}, "pupil_right": {"position": {"0.1667": {"vector": [0, 0, 0]}, "0.2083": {"vector": [0.70643, -0.03084, 0]}, "0.375": {"vector": [0.70643, -0.03084, 0]}, "0.4167": {"vector": [-0.70643, 0.03084, 0]}, "0.625": {"vector": [-0.70643, 0.03084, 0]}, "0.6667": {"vector": [0, 0, 0]}}}, "jaw": {"rotation": {"0.0": {"vector": [-5, 0, 0]}, "0.5": {"vector": [2.5, 0, 0]}, "1.0": {"vector": [-5, 0, 0]}}}}}}, "geckolib_format_version": 2}