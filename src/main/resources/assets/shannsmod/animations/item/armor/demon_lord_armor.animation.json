{"format_version": "1.8.0", "animations": {"misc.idle": {"loop": true, "animation_length": 1, "bones": {"body_left": {"rotation": {"0.0": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [14.93338, 23.11915, -6.32083]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}}}, "body_right": {"rotation": {"0.0": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [14.93338, -23.11915, 6.32083]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}}}}}}, "geckolib_format_version": 2}