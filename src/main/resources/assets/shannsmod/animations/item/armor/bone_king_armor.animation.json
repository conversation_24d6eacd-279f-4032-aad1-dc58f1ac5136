{"format_version": "1.8.0", "animations": {"misc.idle": {"loop": true, "animation_length": 2, "bones": {"head_down": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "1.0": {"vector": [-5, 0, 0]}, "2.0": {"vector": [0, 0, 0]}}}, "arm_left2": {"rotation": {"0.0": {"vector": [2.5, 0, 0]}, "0.0833": {"vector": [-2.5, 0, 0]}, "0.1667": {"vector": [2.5, 0, 0]}, "0.25": {"vector": [-2.5, 0, 0]}, "0.3333": {"vector": [2.5, 0, 0]}, "0.4167": {"vector": [-2.5, 0, 0]}, "0.5": {"vector": [2.5, 0, 0]}, "0.5833": {"vector": [-2.5, 0, 0]}, "0.6667": {"vector": [2.5, 0, 0]}, "0.75": {"vector": [-2.5, 0, 0]}, "0.8333": {"vector": [2.5, 0, 0]}, "0.9167": {"vector": [-2.5, 0, 0]}, "1.0": {"vector": [2.5, 0, 0]}, "1.0833": {"vector": [-2.5, 0, 0]}, "1.1667": {"vector": [2.5, 0, 0]}, "1.25": {"vector": [-2.5, 0, 0]}, "1.3333": {"vector": [2.5, 0, 0]}, "1.4167": {"vector": [-2.5, 0, 0]}, "1.5": {"vector": [2.5, 0, 0]}, "1.5833": {"vector": [-2.5, 0, 0]}, "1.6667": {"vector": [2.5, 0, 0]}, "1.75": {"vector": [-2.5, 0, 0]}, "1.8333": {"vector": [2.5, 0, 0]}, "1.9167": {"vector": [-2.5, 0, 0]}, "2.0": {"vector": [2.5, 0, 0]}}}, "arm_right2": {"rotation": {"0.0": {"vector": [-2.5, 0, 0]}, "0.0833": {"vector": [2.5, 0, 0]}, "0.1667": {"vector": [-2.5, 0, 0]}, "0.25": {"vector": [2.5, 0, 0]}, "0.3333": {"vector": [-2.5, 0, 0]}, "0.4167": {"vector": [2.5, 0, 0]}, "0.5": {"vector": [-2.5, 0, 0]}, "0.5833": {"vector": [2.5, 0, 0]}, "0.6667": {"vector": [-2.5, 0, 0]}, "0.75": {"vector": [2.5, 0, 0]}, "0.8333": {"vector": [-2.5, 0, 0]}, "0.9167": {"vector": [2.5, 0, 0]}, "1.0": {"vector": [-2.5, 0, 0]}, "1.0833": {"vector": [2.5, 0, 0]}, "1.1667": {"vector": [-2.5, 0, 0]}, "1.25": {"vector": [2.5, 0, 0]}, "1.3333": {"vector": [-2.5, 0, 0]}, "1.4167": {"vector": [2.5, 0, 0]}, "1.5": {"vector": [-2.5, 0, 0]}, "1.5833": {"vector": [2.5, 0, 0]}, "1.6667": {"vector": [-2.5, 0, 0]}, "1.75": {"vector": [2.5, 0, 0]}, "1.8333": {"vector": [-2.5, 0, 0]}, "1.9167": {"vector": [2.5, 0, 0]}, "2.0": {"vector": [-2.5, 0, 0]}}}}}}, "geckolib_format_version": 2}