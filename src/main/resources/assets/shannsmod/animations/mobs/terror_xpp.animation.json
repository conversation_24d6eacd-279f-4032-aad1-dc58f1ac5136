{"format_version": "1.8.0", "animations": {"animation.terror_xpp.idle": {"loop": true, "animation_length": 1, "bones": {"Head": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.5": {"vector": [0, 0, 0]}, "0.5417": {"vector": [0, -5, -2.5]}, "0.5833": {"vector": [0, 5, 2.5]}, "0.625": {"vector": [0, -5, -2.5]}, "0.6667": {"vector": [0, 0, 0]}}}, "Left Arm": {"rotation": {"0.7083": {"vector": [0, 0, 0]}, "0.75": {"vector": [0, 0, -5]}, "0.7917": {"vector": [0, 0, 0]}, "0.8333": {"vector": [0, 0, -5]}, "0.875": {"vector": [0, 0, 0]}}}, "Right Arm": {"rotation": {"0.5": {"vector": [0, 0, 0]}, "0.5417": {"vector": [0, 0, 2.5]}, "0.5833": {"vector": [0, 0, 0]}}}}}, "animation.terror_xpp.walk": {"loop": true, "animation_length": 1, "bones": {"Body": {"rotation": {"0.0": {"post": {"vector": [22.5193, -2.30959, -0.95723]}, "lerp_mode": "catmullrom"}, "0.25": {"post": {"vector": [20, 0, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [22.5193, 2.30959, 0.95723]}, "lerp_mode": "catmullrom"}, "0.75": {"post": {"vector": [20, 0, 0]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [22.5193, -2.30959, -0.95723]}, "lerp_mode": "catmullrom"}}}, "Head": {"rotation": {"0.0": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.0417": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.0833": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.125": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.1667": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.2083": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.25": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.2917": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.3333": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.375": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.4167": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.4583": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.5": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.5417": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.5833": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.625": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.6667": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.7083": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.75": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.7917": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.8333": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.875": {"vector": [-2.74618, 4.87863, -16.57456]}, "0.9167": {"vector": [-2.74618, 9.87863, -14.07456]}, "0.9583": {"vector": [-2.74618, 4.87863, -16.57456]}, "1.0": {"vector": [-2.74618, 14.87863, -11.57456]}}}, "down": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.0833": {"vector": [-20, 0, 0]}, "0.1667": {"vector": [0, 0, 0]}, "0.25": {"vector": [-20, 0, 0]}, "0.3333": {"vector": [0, 0, 0]}, "0.4167": {"vector": [-20, 0, 0]}, "0.5": {"vector": [0, 0, 0]}, "0.5833": {"vector": [-20, 0, 0]}, "0.6667": {"vector": [0, 0, 0]}, "0.75": {"vector": [-20, 0, 0]}, "0.8333": {"vector": [0, 0, 0]}, "0.9167": {"vector": [-20, 0, 0]}, "1.0": {"vector": [0, 0, 0]}}}, "Left Arm": {"rotation": {"0.0": {"vector": [-37.5, 0, -12.5]}, "0.5": {"vector": [-37.5, 0, -12.5]}, "0.5417": {"vector": [-37.5, 0, -22.5]}, "0.5833": {"vector": [-37.5, 0, -12.5]}, "0.625": {"vector": [-37.5, 0, -22.5]}, "0.6667": {"vector": [-37.5, 0, -12.5]}, "0.7083": {"vector": [-37.5, 0, -22.5]}, "0.75": {"vector": [-37.5, 0, -12.5]}, "0.7917": {"vector": [-37.5, 0, -22.5]}, "0.8333": {"vector": [-37.5, 0, -12.5]}, "0.875": {"vector": [-37.5, 0, -22.5]}, "0.9167": {"vector": [-37.5, 0, -12.5]}}}, "Left_Arm2": {"rotation": {"0.0": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}, "0.25": {"post": {"vector": [-7.5, 0, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}, "0.75": {"post": {"vector": [-7.5, 0, 0]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [0, 0, 0]}, "lerp_mode": "catmullrom"}}}, "Right Arm": {"rotation": {"0.0": {"vector": [-37.5, 0, 12.5]}, "0.0417": {"vector": [-37.5, 0, 22.5]}, "0.0833": {"vector": [-37.5, 0, 12.5]}, "0.125": {"vector": [-37.5, 0, 22.5]}, "0.1667": {"vector": [-37.5, 0, 12.5]}, "0.2083": {"vector": [-37.5, 0, 22.5]}, "0.25": {"vector": [-37.5, 0, 12.5]}, "0.2917": {"vector": [-37.5, 0, 22.5]}, "0.3333": {"vector": [-37.5, 0, 12.5]}, "0.375": {"vector": [-37.5, 0, 22.5]}, "0.4167": {"vector": [-37.5, 0, 12.5]}}}, "Right Leg": {"rotation": {"0.0": {"post": {"vector": [-32.5, 0, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [42.86934, -17.39026, 8.93828]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [-32.5, 0, 0]}, "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": {"vector": [0, 1, -1]}, "lerp_mode": "catmullrom"}, "0.25": {"post": {"vector": [0, 0, -1]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [0, 1, 1]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [0, 1, -1]}, "lerp_mode": "catmullrom"}}}, "Right_Leg2": {"rotation": {"0.0": {"post": {"vector": [2.81, 0, 0]}, "lerp_mode": "catmullrom"}, "0.1667": {"post": {"vector": [-12.5, 0, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [22.5, 0, 0]}, "lerp_mode": "catmullrom"}, "0.8333": {"post": {"vector": [22.5, 0, 0]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [2.81, 0, 0]}, "lerp_mode": "catmullrom"}}}, "Left Leg": {"rotation": {"0.0": {"post": {"vector": [42.86934, 17.39026, -8.93828]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [-32.5, 0, 0]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [42.86934, 17.39026, -8.93828]}, "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": {"vector": [0, 1, 1]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [0, 1, -1]}, "lerp_mode": "catmullrom"}, "0.75": {"post": {"vector": [0, 0, -1]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [0, 1, 1]}, "lerp_mode": "catmullrom"}}}, "Left_Leg2": {"rotation": {"0.0": {"post": {"vector": [22.5, 0, 0]}, "lerp_mode": "catmullrom"}, "0.3333": {"post": {"vector": [22.5, 0, 0]}, "lerp_mode": "catmullrom"}, "0.6667": {"post": {"vector": [-12.5, 0, 0]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [22.5, 0, 0]}, "lerp_mode": "catmullrom"}}}, "terror_xpp": {"position": {"0.0": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}, "0.125": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}, "0.2083": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}, "0.25": {"post": {"vector": [0, -1, 0]}, "lerp_mode": "catmullrom"}, "0.5": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}, "0.625": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}, "0.7083": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}, "0.75": {"post": {"vector": [0, -1, 0]}, "lerp_mode": "catmullrom"}, "1.0": {"post": {"vector": [0, 1, 0]}, "lerp_mode": "catmullrom"}}}}}, "animation.terror_xpp.attack": {"animation_length": 1, "bones": {"Body": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.2083": {"vector": [-5, 0, 0]}, "0.2917": {"vector": [12.5, 0, 0]}, "0.375": {"vector": [-5, 0, 0]}, "0.5": {"vector": [0, 0, 0]}}}, "Head": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.25": {"vector": [-60, 0, 0]}, "0.2917": {"vector": [42.5, 0, 0]}, "0.3333": {"vector": [40, 0, 0]}, "0.375": {"vector": [42.5, 0, 0]}, "0.5": {"vector": [0, 0, 0]}}}, "down": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.2083": {"vector": [52.5, 0, 0]}, "0.2917": {"vector": [-52.5, 0, 0]}, "0.375": {"vector": [-52.5, 0, 0]}, "0.5": {"vector": [-52.5, 0, 0]}, "1.0": {"vector": [0, 0, 0]}}}, "Left Arm": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.2083": {"vector": [-12.5, 0, 0]}, "0.2917": {"vector": [30.00135, 13.12479, -21.46872]}, "0.5": {"vector": [0, 0, 0]}}}, "Right Arm": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.2083": {"vector": [-12.5, 0, 0]}, "0.2917": {"vector": [30.00135, -13.12479, 21.46872]}, "0.5": {"vector": [0, 0, 0]}}}}}, "animation.terror_xpp.teleport": {"animation_length": 0.5, "bones": {"terror_xpp": {"scale": {"0.0": {"vector": [1, 1, 1]}, "0.25": {"vector": [0.1, 0.1, 0.1]}, "0.5": {"vector": [1, 1, 1]}}}, "Body": {"rotation": {"0.0": {"vector": [0, 0, 0]}, "0.25": {"vector": [0, 360, 0]}, "0.5": {"vector": [0, 0, 0]}}}}}}, "geckolib_format_version": 2}