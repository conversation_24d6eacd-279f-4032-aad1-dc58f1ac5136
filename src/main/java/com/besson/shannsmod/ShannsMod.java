package com.besson.shannsmod;

import com.besson.shannsmod.entity.ModEntities;
import com.besson.shannsmod.entity.ModEntitySpawns;
import com.besson.shannsmod.entity.custom.TerrorXppEntity;
import com.besson.shannsmod.item.ModItemGroups;
import com.besson.shannsmod.item.ModItems;
import com.besson.shannsmod.sound.ModSoundEvents;
import net.fabricmc.api.ModInitializer;

import net.fabricmc.fabric.api.object.builder.v1.entity.FabricDefaultAttributeRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ShannsMod implements ModInitializer {
	public static final String MOD_ID = "shannsmod";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	@Override
	public void onInitialize() {
		// This code runs as soon as Minecraft is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.
		ModItems.registerModItems();
		ModItemGroups.registerModItemGroups();
		ModSoundEvents.registerModSoundEvents();

		// 注册实体属性
		FabricDefaultAttributeRegistry.register(ModEntities.TERROR_XPP, TerrorXppEntity.createXppAttributes());

		// 注册实体刷新
		ModEntitySpawns.registerEntitySpawns();

		LOGGER.info("Hello Fabric world!");
	}
}