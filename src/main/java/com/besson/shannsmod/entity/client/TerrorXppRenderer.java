package com.besson.shannsmod.entity.client;

import com.besson.shannsmod.entity.custom.TerrorXppEntity;
import net.minecraft.client.render.RenderLayer;
import net.minecraft.client.render.VertexConsumer;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.entity.EntityRendererFactory;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.Identifier;
import software.bernie.geckolib.cache.object.BakedGeoModel;
import software.bernie.geckolib.renderer.GeoEntityRenderer;
import software.bernie.geckolib.renderer.layer.GeoRenderLayer;

public class TerrorXppRenderer extends GeoEntityRenderer<TerrorXppEntity> {
    public TerrorXppRenderer(EntityRendererFactory.Context ctx) {
        super(ctx, new TerrorXppModel());
        this.shadowRadius = 0.7f;

        // 添加发光层
        addRenderLayer(new TerrorXppGlowLayer(this));
    }

    @Override
    public Identifier getTextureLocation(TerrorXppEntity entity) {
        return Identifier.of("shannsmod", "textures/mobs/terror_xpp.png");
    }

    // 发光层渲染器
    public static class TerrorXppGlowLayer extends GeoRenderLayer<TerrorXppEntity> {
        private static final Identifier GLOW_TEXTURE = Identifier.of("shannsmod", "textures/mobs/terror_xpp_glow.png");

        public TerrorXppGlowLayer(GeoEntityRenderer<TerrorXppEntity> entityRenderer) {
            super(entityRenderer);
        }

        @Override
        public void render(MatrixStack poseStack, TerrorXppEntity animatable, BakedGeoModel bakedModel,
                          RenderLayer renderType, VertexConsumerProvider bufferSource, VertexConsumer buffer,
                          float partialTick, int packedLight, int packedOverlay) {

            // 使用最大亮度渲染发光纹理
            RenderLayer glowRenderType = RenderLayer.getEyes(GLOW_TEXTURE);
            VertexConsumer glowBuffer = bufferSource.getBuffer(glowRenderType);

            // 渲染发光层，使用最大亮度
            this.getRenderer().reRender(bakedModel, poseStack, bufferSource, animatable, glowRenderType,
                    glowBuffer, partialTick, 15728880, packedOverlay, 0xFFFFFFFF);
        }
    }
}

