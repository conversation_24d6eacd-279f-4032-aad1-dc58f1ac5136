package com.besson.shannsmod.entity.client;

import com.besson.shannsmod.ShannsMod;
import com.besson.shannsmod.entity.custom.TerrorXppEntity;
import net.minecraft.util.Identifier;
import software.bernie.geckolib.model.GeoModel;

public class TerrorXppModel extends GeoModel<TerrorXppEntity> {
    @Override
    public Identifier getModelResource(TerrorXppEntity terrorXppEntity) {
        return Identifier.of(ShannsMod.MOD_ID, "geo/mobs/terror_xpp.geo.json");
    }

    @Override
    public Identifier getTextureResource(TerrorXppEntity terrorXppEntity) {
        return Identifier.of(ShannsMod.MOD_ID, "textures/mobs/terror_xpp.png");
    }

    @Override
    public Identifier getAnimationResource(TerrorXppEntity terrorXppEntity) {
        return Identifier.of(ShannsMod.MOD_ID, "animations/mobs/terror_xpp.animation.json");
    }
}
