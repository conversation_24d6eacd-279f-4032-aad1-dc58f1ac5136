package com.besson.shannsmod.entity;

import com.besson.shannsmod.entity.custom.TerrorXppEntity;
import net.fabricmc.fabric.api.object.builder.v1.entity.FabricEntityTypeBuilder;
import net.minecraft.entity.EntityDimensions;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.SpawnGroup;
import net.minecraft.world.World;
import net.minecraft.util.Identifier;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import com.besson.shannsmod.ShannsMod;
import net.minecraft.entity.LivingEntity;

public class ModEntities {
    public static EntityType<ScytheSweepEntity> SCYTHE_SWEEP;

    public static void register() {
        SCYTHE_SWEEP = Registry.register(
                Registries.ENTITY_TYPE,
                Identifier.of(ShannsMod.MOD_ID, "scythe_sweep"),
                FabricEntityTypeBuilder.<ScytheSweepEntity>create(
                                SpawnGroup.MISC,
                                (EntityType<ScytheSweepEntity> entityType, World world) ->
                                        new ScytheSweepEntity(entityType, world)
                        )
                        .dimensions(EntityDimensions.fixed(1.5F, 0.5F))  // 调整碰撞箱大小
                        .trackRangeBlocks(64)
                        .trackedUpdateRate(1)
                        .build()
        );
    }

    // TerrorXpp实体注册 - 支持自然刷新
    public static final EntityType<TerrorXppEntity> TERROR_XPP = Registry.register(
            Registries.ENTITY_TYPE, Identifier.of(ShannsMod.MOD_ID,"terror_xpp"),
            FabricEntityTypeBuilder.<TerrorXppEntity>create(SpawnGroup.MONSTER, TerrorXppEntity::new)
                    .dimensions(EntityDimensions.fixed(1.4f, 2.0f))
                    .trackRangeBlocks(80) // 增加追踪范围
                    .trackedUpdateRate(1)
                    .build());
}
