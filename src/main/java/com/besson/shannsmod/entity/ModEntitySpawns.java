package com.besson.shannsmod.entity;

import com.besson.shannsmod.entity.custom.TerrorXppEntity;
import net.fabricmc.fabric.api.biome.v1.BiomeModifications;
import net.fabricmc.fabric.api.biome.v1.BiomeSelectors;
import net.minecraft.entity.SpawnGroup;
import net.minecraft.entity.SpawnLocationTypes;
import net.minecraft.entity.SpawnRestriction;
import net.minecraft.world.Heightmap;
import net.minecraft.world.biome.BiomeKeys;

public class ModEntitySpawns {
    
    public static void registerEntitySpawns() {
        // 设置TerrorXpp的刷新限制
        SpawnRestriction.register(
            ModEntities.TERROR_XPP, 
            SpawnLocationTypes.ON_GROUND, 
            Heightmap.Type.MOTION_BLOCKING_NO_LEAVES, 
            TerrorXppEntity::canSpawn
        );

        // 在主世界的所有生物群系中添加TerrorXpp刷新
        BiomeModifications.addSpawn(
            BiomeSelectors.foundInOverworld(), // 主世界所有生物群系
            SpawnGroup.MONSTER, // 怪物组
            ModEntities.TERROR_XPP, // 实体类型
            1, // 权重（稀有度，越低越稀有）
            1, // 最小群体大小
            1  // 最大群体大小
        );
    }
}
