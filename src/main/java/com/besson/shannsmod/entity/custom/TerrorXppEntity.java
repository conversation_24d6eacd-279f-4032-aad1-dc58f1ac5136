package com.besson.shannsmod.entity.custom;

import com.besson.shannsmod.sound.ModSoundEvents;
import net.minecraft.entity.*;
import net.minecraft.entity.ai.TargetPredicate;
import net.minecraft.entity.ai.goal.*;
import net.minecraft.entity.attribute.DefaultAttributeContainer;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.loot.LootTable;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundEvent;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import software.bernie.geckolib.animatable.GeoEntity;
import software.bernie.geckolib.animatable.instance.AnimatableInstanceCache;
import software.bernie.geckolib.animation.*;
import software.bernie.geckolib.util.GeckoLibUtil;

import java.util.EnumSet;

public class TerrorXppEntity extends HostileEntity implements GeoEntity {
    public final AnimatableInstanceCache cache = GeckoLibUtil.createInstanceCache(this);

    // 核心配置 - 根据需求重新设计
    public static final float ATTACK_RANGE = 2.5F;
    public static final float TELEPORT_DISTANCE = 2.0F; // 瞬移到玩家前方的距离
    public static final float ATTACK_DAMAGE = 12.0F;
    public static final int ATTACK_ANIMATION_LENGTH = 20; // 1秒动画
    public static final int DAMAGE_FRAME = 10; // 0.5秒伤害帧
    public static final int ATTACK_COOLDOWN = 30; // 1.5秒攻击冷却
    public static final double HATRED_RANGE = 40.0; // 仇恨范围40格
    public static final double MIN_TELEPORT_DISTANCE = 4.0; // 最小瞬移距离4格
    public static final double MAX_TELEPORT_DISTANCE = 40.0; // 最大瞬移距离40格
    public static final int TELEPORT_COOLDOWN = 200; // 10秒瞬移冷却
    public static final double EFFECT_RANGE = 40.0; // 负面效果范围40格
    private static final int DARKNESS_DURATION = 200; // 10秒黑暗效果
    private static final int NAUSEA_DURATION = 200; // 10秒反胃效果
    private static final int EFFECT_APPLY_INTERVAL = 20; // 效果应用间隔1秒
    private static final int RETALIATION_COOLDOWN = 30; // 反击冷却时间1.5秒

    // 状态变量
    public LivingEntity currentAttackTarget;
    public int attackAnimationTick = 0;
    public int attackCooldown = 0;
    public int teleportCooldown = 0; // 瞬移冷却计时器
    public int effectApplyTimer = 0; // 效果应用计时器
    public int retaliationCooldown = 0; // 反击冷却计时器

    public boolean isAttacking = false;
    public boolean isTeleporting = false; // 瞬移状态标记
    public boolean hasTeleported = false; // 标记是否已瞬移

    public TerrorXppEntity(EntityType<? extends HostileEntity> entityType, World world) {
        super(entityType, world);
        this.experiencePoints = 40;
    }

    public static DefaultAttributeContainer.Builder createXppAttributes() {
        return HostileEntity.createHostileAttributes()
                .add(EntityAttributes.GENERIC_MAX_HEALTH, 500.0)
                .add(EntityAttributes.GENERIC_MOVEMENT_SPEED, 0.25) // 有仇恨时可以移动
                .add(EntityAttributes.GENERIC_ATTACK_DAMAGE, ATTACK_DAMAGE)
                .add(EntityAttributes.GENERIC_FOLLOW_RANGE, HATRED_RANGE) // 仇恨范围40格
                .add(EntityAttributes.GENERIC_KNOCKBACK_RESISTANCE, 1.0)
                .add(EntityAttributes.GENERIC_JUMP_STRENGTH, 0.5);
    }

    @Override
    public void initGoals() {
        // 游泳目标
        this.goalSelector.add(0, new SwimGoal(this));

        // 瞬移攻击目标（远距离）
        this.goalSelector.add(1, new TeleportAttackGoal(this));

        // 近战攻击目标（近距离）
        this.goalSelector.add(2, new MeleeAttackGoal(this, 1.0, false) {
            @Override
            public void attack(LivingEntity target) {
                TerrorXppEntity.this.performMeleeAttack(target);
            }
        });

        // 移动追击目标
        this.goalSelector.add(3, new WanderAroundFarGoal(this, 1.0));

        // 目标选择 - 玩家优先，其次反击
        this.targetSelector.add(0, new RetaliateGoal(this));
        this.targetSelector.add(1, new ActiveTargetGoal<>(this, PlayerEntity.class, true));
        this.targetSelector.add(2, new ActiveTargetGoal<>(this, IronGolemEntity.class, true));

        // 观察目标
        this.goalSelector.add(4, new LookAtEntityGoal(this, PlayerEntity.class, (float)HATRED_RANGE));
        this.goalSelector.add(5, new LookAroundGoal(this));
    }

    // 自定义瞬移攻击目标类 - 只负责瞬移，不负责近战
    private static class TeleportAttackGoal extends Goal {
        private final TerrorXppEntity entity;
        private LivingEntity target;
        private int cooldownTicks = 0;

        public TeleportAttackGoal(TerrorXppEntity entity) {
            this.entity = entity;
            this.setControls(EnumSet.of(Goal.Control.MOVE));
        }

        @Override
        public boolean canStart() {
            this.target = this.entity.getTarget();
            if (this.target == null || !this.target.isAlive()) return false;

            double distance = Math.sqrt(this.entity.squaredDistanceTo(this.target));

            // 只有在瞬移范围内且冷却完成时才能开始
            return distance >= MIN_TELEPORT_DISTANCE &&
                   distance <= MAX_TELEPORT_DISTANCE &&
                   this.entity.teleportCooldown <= 0 &&
                   this.entity.attackCooldown <= 0 &&
                   !this.entity.isAttacking;
        }

        @Override
        public boolean shouldContinue() {
            return false; // 瞬移是一次性动作，不需要持续
        }

        @Override
        public void start() {
            if (this.target != null) {
                this.entity.performTeleportAttack(this.target);
            }
        }
    }

    // 执行瞬移攻击
    public void performTeleportAttack(LivingEntity target) {
        if (target == null || getWorld().isClient || isAttacking || isTeleporting) return;

        this.currentAttackTarget = target;
        this.isTeleporting = true;

        // 瞬移到目标前方
        if (teleportToTarget(target)) {
            // 瞬移成功后开始攻击
            this.isAttacking = true;
            this.attackAnimationTick = 1;
            this.attackCooldown = ATTACK_COOLDOWN;
            this.teleportCooldown = TELEPORT_COOLDOWN;

            // 同步到客户端 - 确保动画触发
            if (!getWorld().isClient) {
                getWorld().sendEntityStatus(this, (byte) 5); // 瞬移状态
                // 延迟一点发送攻击状态，确保瞬移动画先播放
                getWorld().sendEntityStatus(this, (byte) 4); // 攻击状态
            }

            // 播放音效
            playSound(SoundEvents.ENTITY_ENDERMAN_TELEPORT, 1.0F, 1.0F);
            playSound(ModSoundEvents.TERROR_XPP_ATTACK, 1.5F, 1.0F);
        }

        this.isTeleporting = false;
    }

    // 执行近战攻击
    public void performMeleeAttack(LivingEntity target) {
        if (target == null || getWorld().isClient || isAttacking) return;

        this.currentAttackTarget = target;
        this.isAttacking = true;
        this.attackAnimationTick = 1;
        this.attackCooldown = ATTACK_COOLDOWN;

        // 同步到客户端 - 确保动画触发
        if (!getWorld().isClient) {
            getWorld().sendEntityStatus(this, (byte) 4);
        }
        playSound(ModSoundEvents.TERROR_XPP_ATTACK, 1.5F, 1.0F);
    }

    @Override
    public void tick() {
        super.tick();

        // 动态调整移动速度
        updateMovementSpeed();

        // 冷却倒计时
        if (attackCooldown > 0) {
            attackCooldown--;
        }
        if (teleportCooldown > 0) {
            teleportCooldown--;
        }
        if (retaliationCooldown > 0) {
            retaliationCooldown--;
        }

        // 效果应用计时器
        effectApplyTimer++;
        if (effectApplyTimer >= EFFECT_APPLY_INTERVAL) {
            applyAreaEffects();
            effectApplyTimer = 0;
        }

        // 攻击动画逻辑
        if (attackAnimationTick > 0) {
            // 检查目标是否有效
            boolean targetValid = currentAttackTarget != null
                    && currentAttackTarget.isAlive()
                    && this.squaredDistanceTo(currentAttackTarget) <= HATRED_RANGE * HATRED_RANGE;

            if (!targetValid) {
                resetAttackState();
                return;
            }

            attackAnimationTick++;

            // 伤害帧时造成伤害
            if (attackAnimationTick == DAMAGE_FRAME) {
                if (currentAttackTarget != null && this.squaredDistanceTo(currentAttackTarget) <= ATTACK_RANGE * ATTACK_RANGE) {
                    currentAttackTarget.damage(getDamageSources().mobAttack(this), ATTACK_DAMAGE);
                    applyDarknessEffect(currentAttackTarget);
                }
            }

            // 动画播放完毕后重置
            if (attackAnimationTick > ATTACK_ANIMATION_LENGTH) {
                resetAttackState();
            }
        }
    }

    // 动态调整移动速度
    private void updateMovementSpeed() {
        LivingEntity target = this.getTarget();
        double currentSpeed = this.getAttributeValue(EntityAttributes.GENERIC_MOVEMENT_SPEED);

        if (target != null && target.isAlive()) {
            // 有目标时可以移动
            if (currentSpeed < 0.2) {
                this.getAttributeInstance(EntityAttributes.GENERIC_MOVEMENT_SPEED).setBaseValue(0.25);
            }
        } else {
            // 没有目标时不移动
            if (currentSpeed > 0.0) {
                this.getAttributeInstance(EntityAttributes.GENERIC_MOVEMENT_SPEED).setBaseValue(0.0);
            }
        }
    }

    // 重置攻击状态
    private void resetAttackState() {
        attackAnimationTick = 0;
        isAttacking = false;
        isTeleporting = false;
        currentAttackTarget = null;
        hasTeleported = false;
    }

    // 重写damage方法以处理反击逻辑
    @Override
    public boolean damage(DamageSource source, float amount) {
        // 基本伤害处理
        boolean result = super.damage(source, amount);

        // 如果受到伤害并且不在攻击冷却中，尝试反击
        // 修复：使用getWorld()替代直接访问world字段
        if (result && !this.getWorld().isClient && retaliationCooldown <= 0) {
            Entity attacker = source.getAttacker();
            if (attacker instanceof LivingEntity) {
                LivingEntity livingAttacker = (LivingEntity) attacker;
                // 设置攻击者为目标
                this.setTarget(livingAttacker);
                // 设置反击冷却
                retaliationCooldown = RETALIATION_COOLDOWN;

                // 播放受伤音效（如果还没有播放）
                playSound(getHurtSound(source), getSoundVolume(), getSoundPitch());

                // 如果当前没有攻击目标，立即开始攻击
                if (!isAttacking && attackCooldown <= 0) {
                    this.currentAttackTarget = livingAttacker;
                    this.isAttacking = true;
                    this.attackAnimationTick = 1;
                    this.attackCooldown = ATTACK_COOLDOWN;
                    this.hasTeleported = false;

                    // 强制同步到客户端
                    getWorld().sendEntityStatus(this, (byte) 4);
                }
            }
        }

        return result;
    }

    // 反击目标选择器
    private static class RetaliateGoal extends TrackTargetGoal {
        public RetaliateGoal(TerrorXppEntity entity) {
            super(entity, false);
        }

        @Override
        public boolean canStart() {
            LivingEntity target = this.mob.getAttacker();
            return target != null && this.canTrack(target, TargetPredicate.DEFAULT);
        }

        @Override
        public void start() {
            this.mob.setTarget(this.mob.getAttacker());
            super.start();
        }
    }

    // 其他方法保持不变...
    // 对范围内的玩家施加黑暗和反胃效果（仅在有仇恨时）
    private void applyAreaEffects() {
        if (getWorld().isClient || getTarget() == null) return;

        // 获取当前实体位置
        Vec3d position = getPos();
        // 创建以实体为中心的Box
        Box areaBox = new Box(
                position.getX() - EFFECT_RANGE,
                position.getY() - EFFECT_RANGE,
                position.getZ() - EFFECT_RANGE,
                position.getX() + EFFECT_RANGE,
                position.getY() + EFFECT_RANGE,
                position.getZ() + EFFECT_RANGE
        );

        // 获取范围内的所有玩家
        double rangeSquared = EFFECT_RANGE * EFFECT_RANGE;
        for (PlayerEntity player : getWorld().getEntitiesByClass(
                PlayerEntity.class,
                areaBox,
                entity -> this.squaredDistanceTo(entity) <= rangeSquared && entity.isAlive()
        )) {
            // 施加黑暗效果
            player.addStatusEffect(new StatusEffectInstance(
                    StatusEffects.DARKNESS,
                    DARKNESS_DURATION,
                    0,
                    false,
                    false,
                    true
            ));

            // 施加反胃效果
            player.addStatusEffect(new StatusEffectInstance(
                    StatusEffects.NAUSEA,
                    NAUSEA_DURATION,
                    0,
                    false,
                    false,
                    true
            ));

            // 添加恐怖粒子效果
            if (getWorld() instanceof ServerWorld serverWorld) {
                double dx = player.getX() - getX();
                double dy = player.getY() - getY();
                double dz = player.getZ() - getZ();

                // 在TerrorXpp和玩家之间创建黑暗粒子轨迹
                for (int i = 0; i < 15; i++) {
                    double posX = getX() + dx * i / 15;
                    double posY = getY() + dy * i / 15 + 1.0;
                    double posZ = getZ() + dz * i / 15;

                    serverWorld.spawnParticles(
                            ParticleTypes.SMOKE,
                            posX, posY, posZ,
                            2, 0.1, 0.1, 0.1, 0.05
                    );

                    // 添加一些恐怖的粒子效果
                    serverWorld.spawnParticles(
                            ParticleTypes.ASH,
                            posX, posY, posZ,
                            1, 0.05, 0.05, 0.05, 0.02
                    );
                }
            }
        }
    }


    // 瞬移到目标前方
    private boolean teleportToTarget(LivingEntity target) {
        if (target == null || getWorld().isClient) return false;

        // 获取目标朝向
        Vec3d targetForward = Vec3d.fromPolar(0, target.getYaw()).normalize();

        // 计算目标前方的位置
        Vec3d targetPos = target.getPos();
        Vec3d teleportPos = targetPos.add(targetForward.multiply(-TELEPORT_DISTANCE));

        // 确保瞬移位置安全（有站立空间）
        BlockPos teleportBlockPos = new BlockPos(
                (int)teleportPos.getX(),
                (int)teleportPos.getY(),
                (int)teleportPos.getZ()
        );

        if (isValidTeleportPosition(teleportBlockPos)) {
            ServerWorld serverWorld = (ServerWorld) getWorld();

            // 出发点粒子效果
            for (int i = 0; i < 30; i++) {
                serverWorld.spawnParticles(
                        ParticleTypes.PORTAL,
                        getX() + (random.nextDouble() - 0.5) * getWidth() * 2,
                        getY() + random.nextDouble() * getHeight(),
                        getZ() + (random.nextDouble() - 0.5) * getWidth() * 2,
                        1, 0, 0.5, 0, 0.2
                );
            }

            // 执行瞬移
            refreshPositionAndAngles(teleportPos.x, teleportPos.y, teleportPos.z,
                                   target.getYaw() + 180, getPitch()); // 面向目标

            // 到达点粒子效果
            for (int i = 0; i < 30; i++) {
                serverWorld.spawnParticles(
                        ParticleTypes.PORTAL,
                        teleportPos.x + (random.nextDouble() - 0.5) * getWidth() * 2,
                        teleportPos.y + random.nextDouble() * getHeight(),
                        teleportPos.z + (random.nextDouble() - 0.5) * getWidth() * 2,
                        1, 0, 0.5, 0, 0.2
                );
            }

            return true;
        }
        return false;
    }

    // 检查瞬移位置是否安全
    private boolean isValidTeleportPosition(BlockPos pos) {
        // 检查目标位置是否有足够空间
        Box box = new Box(pos).expand(0.5, 0, 0.5).stretch(0, getHeight(), 0);
        return getWorld().isSpaceEmpty(this, box) && !getWorld().getBlockState(pos.down()).isAir();
    }

    // 施加黑暗效果（仅用于攻击时）
    public void applyDarknessEffect(LivingEntity target) {
        if (target != null && target.isAlive() && !getWorld().isClient) {
            target.addStatusEffect(new StatusEffectInstance(
                    StatusEffects.DARKNESS,
                    DARKNESS_DURATION,
                    0,
                    false,
                    false,
                    true
            ));
        }
    }

    // 客户端同步
    @Override
    public void handleStatus(byte status) {
        if (status == 4) {
            // 攻击状态同步
            this.isAttacking = true;
            this.attackAnimationTick = 1;
            // 强制重置动画控制器
            if (getWorld().isClient) {
                this.triggerAnim("main_controller", "attack");
            }
        } else if (status == 5) {
            // 瞬移状态同步
            this.isTeleporting = true;
            if (getWorld().isClient) {
                this.triggerAnim("main_controller", "teleport");
            }
        } else {
            super.handleStatus(status);
        }
    }

    @Override
    public void registerControllers(AnimatableManager.ControllerRegistrar registrar) {
        // 主动画控制器
        registrar.add(new AnimationController<>(this, "main_controller", 0, state -> {
            // 瞬移动画优先级最高
            if (isTeleporting && attackAnimationTick <= 0) {
                return state.setAndContinue(RawAnimation.begin().then("animation.terror_xpp.teleport", Animation.LoopType.PLAY_ONCE));
            }

            // 攻击动画
            if (isAttacking && attackAnimationTick > 0) {
                return state.setAndContinue(RawAnimation.begin().then("animation.terror_xpp.attack", Animation.LoopType.PLAY_ONCE));
            }

            // 行走动画
            if (state.isMoving()) {
                return state.setAndContinue(RawAnimation.begin().then("animation.terror_xpp.walk", Animation.LoopType.LOOP));
            }

            // 待机动画
            return state.setAndContinue(RawAnimation.begin().then("animation.terror_xpp.idle", Animation.LoopType.LOOP));
        }));

        // 添加攻击触发器
        registrar.add(new AnimationController<>(this, "attack_controller", 0, state -> {
            return PlayState.STOP;
        }).triggerableAnim("attack", RawAnimation.begin().then("animation.terror_xpp.attack", Animation.LoopType.PLAY_ONCE))
          .triggerableAnim("teleport", RawAnimation.begin().then("animation.terror_xpp.teleport", Animation.LoopType.PLAY_ONCE)));
    }

    @Override
    public SoundEvent getHurtSound(DamageSource source) {
        return ModSoundEvents.TERROR_XPP_HURT;
    }

    @Override
    public SoundEvent getDeathSound() {
        return ModSoundEvents.TERROR_XPP_DEATH;
    }

    @Override
    public AnimatableInstanceCache getAnimatableInstanceCache() {
        return cache;
    }
    private static final RegistryKey<LootTable> LOOT_TABLE_KEY = RegistryKey.of(
            RegistryKeys.LOOT_TABLE,
            Identifier.of("shannsmod", "entities/terror_xpp") // 战利品表路径
    );
    @Override
    public RegistryKey<LootTable> getLootTableId() {
        return LOOT_TABLE_KEY;
    }

    // 刷新条件 - 主世界全天刷新，但稀有
    public static boolean canSpawn(EntityType<TerrorXppEntity> type, net.minecraft.world.ServerWorldAccess world,
                                  net.minecraft.entity.SpawnReason spawnReason, BlockPos pos,
                                  net.minecraft.util.math.random.Random random) {
        // 基本的敌对生物刷新条件
        boolean basicConditions = HostileEntity.canSpawnInDark(type, world, spawnReason, pos, random);

        // 额外的稀有度检查 - 只有0.1%的概率通过
        boolean rareCheck = random.nextFloat() < 0.001f;

        // 确保在主世界
        boolean isOverworld = world.toServerWorld().getRegistryKey() == net.minecraft.world.World.OVERWORLD;

        return basicConditions && rareCheck && isOverworld;
    }
}
