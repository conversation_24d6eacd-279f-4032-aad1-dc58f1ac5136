package com.besson.shannsmod.entity;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.MathHelper;
import net.minecraft.world.World;
// 修正1：根据你的实体注册类实际位置修改导入（如果ModEntities在entity包下）
import com.besson.shannsmod.entity.ModEntities;

// 镰刀攻击特效实体
public class ScytheSweepEntity extends Entity {
    private LivingEntity owner; // 攻击的所有者（玩家）
    private static final int LIFETIME = 10; // 实体存在时间（10tick = 0.5秒）
    private static final float RADIUS = 4.0F; // 扇形范围半径

    // 必须的构造方法（注册实体时使用）
    public ScytheSweepEntity(EntityType<? extends ScytheSweepEntity> type, World world) {
        super(type, world);
        this.noClip = true; // 不与其他实体碰撞
    }

    // 玩家创建特效时使用的构造方法
    public ScytheSweepEntity(World world, LivingEntity owner) {
        // 修正2：引用正确的实体注册类（确保ModEntities存在且SCYTHE_SWEEP已定义）
        this(ModEntities.SCYTHE_SWEEP, world);
        this.owner = owner;
        this.setPosition(owner.getEyePos()); // 从玩家眼睛位置生成
    }

    // 实现抽象方法（必须重写）
    @Override
    protected void initDataTracker(DataTracker.Builder builder) {
        // 无需追踪数据，空实现即可
    }

    // 实体每tick更新
    @Override
    public void tick() {
        super.tick();
        if (this.owner == null || !this.owner.isAlive()) {
            this.discard(); // 所有者死亡则移除特效
            return;
        }

        // 客户端生成扇形粒子特效
        if (this.getWorld().isClient()) {
            spawnSweepParticles();
        }

        // 超过存在时间则移除
        if (this.age >= LIFETIME) {
            this.discard();
        }
    }

    // 生成扇形粒子
    private void spawnSweepParticles() {
        World world = this.getWorld();
        if (!(world instanceof ServerWorld serverWorld)) return;

        // 获取玩家朝向（弧度）
        float yawRadians = (float) Math.toRadians(this.owner.getYaw());
        // 120度扇形 = 左右各60度（π/3弧度）
        float startAngle = yawRadians - (float) Math.PI / 3;
        float endAngle = yawRadians + (float) Math.PI / 3;

        // 沿扇形边缘生成粒子
        for (float angle = startAngle; angle <= endAngle; angle += 0.1F) {
            // 计算粒子位置（基于玩家位置的扇形边缘）
            float x = (float) (this.getX() + RADIUS * MathHelper.cos(angle));
            float z = (float) (this.getZ() + RADIUS * MathHelper.sin(angle));
            // 生成攻击特效粒子
            serverWorld.spawnParticles(
                    ParticleTypes.SWEEP_ATTACK,
                    x, this.getY() + 0.5, z, // Y轴偏移0.5避免贴地
                    1, 0, 0, 0, 0 // 粒子速度为0
            );
        }
    }

    // 以下方法为必须重写的空实现（无需实际功能）
    @Override
    protected void readCustomDataFromNbt(NbtCompound nbt) {}

    @Override
    protected void writeCustomDataToNbt(NbtCompound nbt) {}

    // 修正3：删除错误的createSpawnPacket重写（无需自定义网络包）
}
