package com.besson.shannsmod.mixin;

import com.besson.shannsmod.item.ModItems;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.damage.DamageSources;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(LivingEntity.class)
public class StarfishMixin {
    private int damageTimer = 0;

    @Inject(method = "tick", at = @At("TAIL"))
    private void onTick(CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        World world = entity.getWorld();

        // 跳过客户端和非玩家实体
        if (world.isClient() || !(entity instanceof PlayerEntity player)) {
            return;
        }

        // 获取伤害源
        DamageSource magicDamage = entity.getDamageSources().magic();

        // 检查主手或副手是否持有海星匕首
        ItemStack mainHand = player.getMainHandStack();
        ItemStack offHand = player.getOffHandStack();
        boolean hasStarfishDagger =
                mainHand.getItem() == ModItems.STARFISH_DAGGER ||
                        offHand.getItem() == ModItems.STARFISH_DAGGER;

        if (hasStarfishDagger) {
            damageTimer++;
            if (damageTimer >= 40) { // 20刻 = 1秒
                player.damage(magicDamage, 3.0F);
                damageTimer = 0;
            }
        } else {
            damageTimer = 0; // 重置计时器
        }
    }
}