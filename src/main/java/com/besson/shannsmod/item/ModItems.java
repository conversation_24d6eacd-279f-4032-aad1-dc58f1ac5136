package com.besson.shannsmod.item;

import com.besson.shannsmod.ShannsMod;
import com.besson.shannsmod.entity.ModEntities;
import com.besson.shannsmod.item.custom.armor.*;
import com.besson.shannsmod.item.custom.bow.*;
import com.besson.shannsmod.item.custom.dagger.DaggerItem;
import com.besson.shannsmod.item.custom.dagger.StarfishDaggerItem;
import com.besson.shannsmod.item.custom.hat.HalloweenPumpkinHatItem;
import com.besson.shannsmod.item.custom.hat.ShannHatItem;
import com.besson.shannsmod.item.custom.scythe.ScytheItem;
import com.besson.shannsmod.item.custom.sword.ShannsOPSwordItem;
import com.besson.shannsmod.item.custom.sword.ShannsSwordItem;
import com.besson.shannsmod.item.materias.dagger.KingdomDaggerToolMaterias;
import com.besson.shannsmod.item.materias.dagger.StarfishDaggerToolMaterias;
import com.besson.shannsmod.item.materias.scythe.ScytheMaterias;
import com.besson.shannsmod.item.materias.tool.*;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.BannerPatternsComponent;
import net.minecraft.component.type.UnbreakableComponent;
import net.minecraft.item.*;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.util.Identifier;

public class ModItems {
    public static final Item HALLOWEEN_PICKAXE = registerItems("halloween_pickaxe",new PickaxeItem(HalloweenToolMaterias.HALLOWEEN_DEBRIS,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(HalloweenToolMaterias.HALLOWEEN_DEBRIS,1.0F,-2.8F))));     //万圣节镐

    public static final Item KINGDOM_PICKAXE = registerItems("kingdom_pickaxe",new PickaxeItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,1.0F,-2.8F))));

    public static final Item BARBARIAN_PICKAXE = registerItems("barbarians_pickaxe",new PickaxeItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,1.0F,-2.8F))));

    public static final Item HALLOWEEN_SWORD = registerItems("halloween_sword",new ShannsSwordItem(HalloweenToolMaterias.HALLOWEEN_DEBRIS,
 new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(HalloweenToolMaterias.HALLOWEEN_DEBRIS,(int)4.5F,-2.4F))));

    public static final Item HALLOWEEN_DEBRIS = registerItems("halloween_debris",new Item(new Item.Settings().fireproof()));     //万圣节碎片

    public static final Item HALLOWEEN_BOW = registerItems("halloween_bow",new HalloweenBowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item HALLOWEEN_SHIELD = registerItems("halloween_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item HALLOWEEN_PUMPKIN_HELMET = registerItems("halloween_pumpkin_helmet",new HalloweenPumpkinHatItem(HalloweenPumpkinHatItem.Type.HELMET,new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(HalloweenPumpkinHatItem.Type.HELMET.getMaxDamage(Integer.MAX_VALUE))));

    public static final Item STRAW_HAT = registerItems("straw_hat",new ShannHatItem(ShannHatItem.Type.HELMET,new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(ShannHatItem.Type.HELMET.getMaxDamage(Integer.MAX_VALUE))));

    public static final Item SHANN_HELMET = registerItems("shann_helmet",new ShannHatItem(ShannHatItem.Type.HELMET,new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(ShannHatItem.Type.HELMET.getMaxDamage(Integer.MAX_VALUE))));

    public static final Item GROUDON_SWORD = registerItems("groudon_sword",new ShannsSwordItem(GroudonToolMaterias.GROUDON_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(GroudonToolMaterias.GROUDON_SWORD,(int)4.5F,-2.4F))));

    public static final Item KYOGRE_SHIELD = registerItems("kyogre_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item KINGDOM_SWORD = registerItems("kingdom_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item BARBARIANS_SWORD = registerItems("barbarians_sword",new ShannsSwordItem(BarbariansToolMaterias.BARBARIANS_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(BarbariansToolMaterias.BARBARIANS_SWORD,(int)4.5F,-2.4F))));

    public static final Item KINGDOM_BOW = registerItems("kingdom_bow",new KingdomBowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item BARBARIANS_BOW = registerItems("barbarians_bow",new BarbariansBowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item BARBARIANS_SHIELD = registerItems("barbarians_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item KINGDOM_SHIELD = registerItems("kingdom_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item SLIME_SWORD = registerItems("slime_sword",new ShannsSwordItem(SlimeToolMaterias.SLIME_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(SlimeToolMaterias.SLIME_SWORD,(int)4.5F,-2.4F))));

    public static final Item STARFISH_DAGGER = registerItems("starfish_dagger",new StarfishDaggerItem(StarfishDaggerToolMaterias.STARFISH_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(StarfishDaggerItem.createAttributeModifiers(StarfishDaggerToolMaterias.STARFISH_DAGGER,(int)0.5F,-2.0F))));

    public static final Item KINGDOM_DAGGER = registerItems("kingdom_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item BARBARIANS_DAGGER = registerItems("barbarians_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item SPRING_DAGGER = registerItems("spring_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item SUMMER_DAGGER = registerItems("summer_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item AUTUMN_DAGGER = registerItems("autumn_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item WINTER_DAGGER = registerItems("winter_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item SPRING_SWORD = registerItems("spring_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item SUMMER_SWORD = registerItems("summer_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item AUTUMN_SWORD = registerItems("autumn_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item WINTER_SWORD = registerItems("winter_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item SPRING_PICKAXE = registerItems("spring_pickaxe",new PickaxeItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,1.0F,-2.8F))));

    public static final Item SUMMER_PICKAXE = registerItems("summer_pickaxe",new PickaxeItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,1.0F,-2.8F))));

    public static final Item AUTUMN_PICKAXE = registerItems("autumn_pickaxe",new PickaxeItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,1.0F,-2.8F))));

    public static final Item WINTER_PICKAXE = registerItems("winter_pickaxe",new PickaxeItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,1.0F,-2.8F))));

    public static final Item SPRING_SHIELD = registerItems("spring_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item SUMMER_SHIELD = registerItems("summer_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item AUTUMN_SHIELD = registerItems("autumn_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item WINTER_SHIELD = registerItems("winter_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item SPRING_BOW = registerItems("spring_bow",new BowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item SUMMER_BOW = registerItems("summer_bow",new BowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item AUTUMN_BOW = registerItems("autumn_bow",new BowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item WINTER_BOW = registerItems("winter_bow",new BowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item SAKURA_BOW = registerItems("sakura_bow",new SakuraBowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item ARCANE_DAGGER = registerItems("arcane_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item MEGA_ARCANE_DAGGER = registerItems("mega_arcane_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item BUTCHER_DAGGER = registerItems("butcher_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item CRYSTALLINE_WESTERN_SWORD = registerItems("crystalline_western_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item PURPLE_CRYSTALLINE_WESTERN_SWORD = registerItems("purple_crystalline_western_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item CELEBRATION_SWORD = registerItems("celebration_sword",new ShannsSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item TENTACLE_SWORD = registerItems("tentacle_sword",new ShannsOPSwordItem(KingdomToolMaterias.KINGDOM_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsOPSwordItem.createAttributeModifiers(KingdomToolMaterias.KINGDOM_SWORD,(int)4.5F,-2.4F))));

    public static final Item CELEBRATION_DAGGER = registerItems("celebration_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item DIALGA_SWORD = registerItems("dialga_sword",new ShannsSwordItem(DialgaToolMaterias.DIALGA_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(DialgaToolMaterias.DIALGA_SWORD,(int)4.5F,-2.4F))));

    public static final Item EXILE_SWORD = registerItems("exile_sword",new ShannsSwordItem(ExileToolMaterias.EXILE_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(ExileToolMaterias.EXILE_SWORD,(int)4.5F,-2.4F))));

    public static final Item DEMON_LORD_SWORD = registerItems("demon_lord_sword",new ShannsSwordItem(DemonLordToolMaterias.DEMON_LORD_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(ShannsSwordItem.createAttributeModifiers(DemonLordToolMaterias.DEMON_LORD_SWORD,(int)4.5F,-2.4F))));

    public static final Item DEMON_LORD_SHIELD = registerItems("demon_lord_shield", new ShieldItem(new Item.Settings().maxDamage(Integer.MAX_VALUE).fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).component(DataComponentTypes.BANNER_PATTERNS, BannerPatternsComponent.DEFAULT)));

    public static final Item DEMON_LORD_BOW = registerItems("demon_lord_bow",new HalloweenBowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item DEMON_LORD_PICKAXE = registerItems("demon_lord_pickaxe",new PickaxeItem(DemonLordToolMaterias.DEMON_LORD_SWORD,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(PickaxeItem.createAttributeModifiers(DemonLordToolMaterias.DEMON_LORD_SWORD,1.0F,-2.8F))));

    public static final Item SHADOW_ASSASSIN_DAGGER = registerItems("shadow_assassin_dagger",new DaggerItem(KingdomDaggerToolMaterias.KINGDOM_DAGGER,
            new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).attributeModifiers(DaggerItem.createAttributeModifiers(KingdomDaggerToolMaterias.KINGDOM_DAGGER,(int)0.5F,-2.0F))));

    public static final Item SHADOW_ASSASSIN_BOW = registerItems("shadow_assassin_bow",new BowItems(new Item.Settings().fireproof().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE)));

    public static final Item DEMON_LORD_SCYTHE = registerItems(
            "demon_lord_scythe",
            new ScytheItem(
                    ScytheMaterias.DEMON_LORD_SCYTHE, // 自定义镰刀材质（建议新建一个，或复用你的剑材质）
                    new Item.Settings()
                            .fireproof() // 防火
                            .component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)) // 不可破坏
                            .attributeModifiers(ShannsSwordItem.createAttributeModifiers(ScytheMaterias.DEMON_LORD_SCYTHE,1,-2.8F))));

    public static final Item FARMER_SCYTHE = registerItems(
            "farmer_scythe",
            new ScytheItem(
                    ScytheMaterias.DEMON_LORD_SCYTHE, // 自定义镰刀材质（建议新建一个，或复用你的剑材质）
                    new Item.Settings()
                            .fireproof() // 防火
                            .component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)) // 不可破坏
                            .attributeModifiers(ShannsSwordItem.createAttributeModifiers(ScytheMaterias.DEMON_LORD_SCYTHE,1,-2.8F))));

    public static final Item DEATH_SCYTHE = registerItems(
            "death_scythe",
            new ScytheItem(
                    ScytheMaterias.DEMON_LORD_SCYTHE, // 自定义镰刀材质（建议新建一个，或复用你的剑材质）
                    new Item.Settings()
                            .fireproof() // 防火
                            .component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)) // 不可破坏
                            .attributeModifiers(ShannsSwordItem.createAttributeModifiers(ScytheMaterias.DEMON_LORD_SCYTHE,1,-2.8F))));


    public static final Item KINGDOM_TRIDENT = registerItems("kingdom_trident", new KingdomTridentItems(new Item.Settings().component(DataComponentTypes.UNBREAKABLE, new UnbreakableComponent(true)).maxDamage(Integer.MAX_VALUE).attributeModifiers(KingdomTridentItems.createAttributeModifiers()).component(DataComponentTypes.TOOL, KingdomTridentItems.createToolComponent())));

    public static final Item ARCANE_HELMET = registerItems("arcane_helmet", new ArcaneArmor(ArcaneArmor.Type.HELMET));

    public static final Item ARCANE_CHESTPLATE = registerItems("arcane_chestplate", new ArcaneArmor(ArcaneArmor.Type.CHESTPLATE));

    public static final Item ARCANE_LEGGINGS = registerItems("arcane_leggings", new ArcaneArmor(ArcaneArmor.Type.LEGGINGS));

    public static final Item ARCANE_BOOTS = registerItems("arcane_boots", new ArcaneArmor(ArcaneArmor.Type.BOOTS));

    public static final Item CELEBRATION_HELMET = registerItems("celebration_helmet", new CelebrationArmor(CelebrationArmor.Type.HELMET));

    public static final Item CELEBRATION_CHESTPLATE = registerItems("celebration_chestplate", new CelebrationArmor(CelebrationArmor.Type.CHESTPLATE));

    public static final Item CELEBRATION_LEGGINGS = registerItems("celebration_leggings", new CelebrationArmor(CelebrationArmor.Type.LEGGINGS));

    public static final Item CELEBRATION_BOOTS = registerItems("celebration_boots", new CelebrationArmor(CelebrationArmor.Type.BOOTS));

    public static final Item CLOWN_HELMET = registerItems("clown_helmet", new ClownArmor(ClownArmor.Type.HELMET));

    public static final Item BONE_KING_HELMET = registerItems("bone_king_helmet", new BoneKingArmor(BoneKingArmor.Type.HELMET));

    public static final Item BONE_KING_CHESTPLATE = registerItems("bone_king_chestplate", new BoneKingArmor(BoneKingArmor.Type.CHESTPLATE));

    public static final Item BONE_KING_LEGGINGS = registerItems("bone_king_leggings", new BoneKingArmor(BoneKingArmor.Type.LEGGINGS));

    public static final Item BONE_KING_BOOTS = registerItems("bone_king_boots", new BoneKingArmor(BoneKingArmor.Type.BOOTS));

    public static final Item SPRING_HELMET = registerItems("spring_helmet", new SpringArmor(SpringArmor.Type.HELMET));

    public static final Item SPRING_CHESTPLATE = registerItems("spring_chestplate", new SpringArmor(SpringArmor.Type.CHESTPLATE));

    public static final Item SPRING_LEGGINGS = registerItems("spring_leggings", new SpringArmor(SpringArmor.Type.LEGGINGS));

    public static final Item SPRING_BOOTS = registerItems("spring_boots", new SpringArmor(SpringArmor.Type.BOOTS));

    public static final Item SUMMER_HELMET = registerItems("summer_helmet", new SummerArmor(SummerArmor.Type.HELMET));

    public static final Item SUMMER_CHESTPLATE = registerItems("summer_chestplate", new SummerArmor(SummerArmor.Type.CHESTPLATE));

    public static final Item SUMMER_LEGGINGS = registerItems("summer_leggings", new SummerArmor(SummerArmor.Type.LEGGINGS));

    public static final Item SUMMER_BOOTS = registerItems("summer_boots", new SummerArmor(SummerArmor.Type.BOOTS));

    public static final Item AUTUMN_HELMET = registerItems("autumn_helmet", new AutumnArmor(AutumnArmor.Type.HELMET));

    public static final Item AUTUMN_CHESTPLATE = registerItems("autumn_chestplate", new AutumnArmor(AutumnArmor.Type.CHESTPLATE));

    public static final Item AUTUMN_LEGGINGS = registerItems("autumn_leggings", new AutumnArmor(AutumnArmor.Type.LEGGINGS));

    public static final Item AUTUMN_BOOTS = registerItems("autumn_boots", new AutumnArmor(AutumnArmor.Type.BOOTS));

    public static final Item WINTER_HELMET = registerItems("winter_helmet", new WinterArmor(WinterArmor.Type.HELMET));

    public static final Item WINTER_CHESTPLATE = registerItems("winter_chestplate", new WinterArmor(WinterArmor.Type.CHESTPLATE));

    public static final Item WINTER_LEGGINGS = registerItems("winter_leggings", new WinterArmor(WinterArmor.Type.LEGGINGS));

    public static final Item WINTER_BOOTS = registerItems("winter_boots", new WinterArmor(WinterArmor.Type.BOOTS));

    public static final Item DEMON_LORD_HELMET = registerItems("demon_lord_helmet", new DemonLordArmor(DemonLordArmor.Type.HELMET));

    public static final Item DEMON_LORD_CHESTPLATE = registerItems("demon_lord_chestplate", new DemonLordArmor(DemonLordArmor.Type.CHESTPLATE));

    public static final Item DEMON_LORD_LEGGINGS = registerItems("demon_lord_leggings", new DemonLordArmor(DemonLordArmor.Type.LEGGINGS));

    public static final Item DEMON_LORD_BOOTS = registerItems("demon_lord_boots", new DemonLordArmor(DemonLordArmor.Type.BOOTS));

    public static final Item SHADOW_ASSASSIN_HELMET = registerItems("shadow_assassin_helmet", new ShadowAssassinArmor(ShadowAssassinArmor.Type.HELMET));

    public static final Item SHADOW_ASSASSIN_CHESTPLATE = registerItems("shadow_assassin_chestplate", new ShadowAssassinArmor(ShadowAssassinArmor.Type.CHESTPLATE));

    public static final Item SHADOW_ASSASSIN_LEGGINGS = registerItems("shadow_assassin_leggings", new ShadowAssassinArmor(ShadowAssassinArmor.Type.LEGGINGS));

    public static final Item SHADOW_ASSASSIN_BOOTS = registerItems("shadow_assassin_boots", new ShadowAssassinArmor(ShadowAssassinArmor.Type.BOOTS));

    public static final Item TERROR_XPP_SPAWN_EGG = registerItems("terror_xpp_spawn_egg",new SpawnEggItem(ModEntities.TERROR_XPP,0x4e5235,	0x333333 ,new Item.Settings()));

    public static Item registerItems(String id, Item item){
//        return Registry.register(Registries.ITEM, RegistryKey.of(Registries.ITEM.getKey(), Identifier.of(ShannsMod.MOD_ID,id)), item);
        return Registry.register(Registries.ITEM, Identifier.of(ShannsMod.MOD_ID,id), item);
    }
//    private static void addItemToIG(FabricItemGroupEntries fabricItemGroupEntries){
//        fabricItemGroupEntries.add(HALLOWEEN_PICKAXE);
//    }
    public static void registerModItems(){
//        ItemGroupEvents.modifyEntriesEvent(ItemGroups.INGREDIENTS).register(ModItems::addItemToIG);

        ShannsMod.LOGGER.info("Registering Items");
    }
}