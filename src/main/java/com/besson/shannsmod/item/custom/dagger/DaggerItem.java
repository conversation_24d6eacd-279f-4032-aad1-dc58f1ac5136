package com.besson.shannsmod.item.custom.dagger;

import com.besson.shannsmod.item.interfaces.IDamageMultiplierWeapon;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.component.type.AttributeModifierSlot;
import net.minecraft.component.type.AttributeModifiersComponent;
import net.minecraft.component.type.ToolComponent;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.attribute.EntityAttributeModifier;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ToolItem;
import net.minecraft.item.ToolMaterial;
import net.minecraft.item.tooltip.TooltipType;
import net.minecraft.registry.tag.BlockTags;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.random.Random;
import net.minecraft.world.World;

import java.util.List;

public class DaggerItem extends ToolItem implements IDamageMultiplierWeapon {
    // 构造方法不变
    public DaggerItem(ToolMaterial toolMaterial, Settings settings) {
        // 这里调用本类的静态方法，必须确保方法存在
        super(toolMaterial, settings.component(DataComponentTypes.TOOL, createToolComponent()));
    }

    // 实现接口：设置海星匕首的伤害倍数概率（示例：20%双倍，5%三倍）
    @Override
    public float getDoubleDamageChance() {
        return 0.4f; // 40%概率
    }

    @Override
    public float getTripleDamageChance() {
        return 0.1f; // 10%概率
    }

    public static AttributeModifiersComponent createAttributeModifiers(ToolMaterial material, int baseAttackDamage, float attackSpeed) {
        return AttributeModifiersComponent.builder()
                // 攻击伤害属性（基础伤害 + 材料附加伤害）
                .add(
                        EntityAttributes.GENERIC_ATTACK_DAMAGE,
                        new EntityAttributeModifier(
                                BASE_ATTACK_DAMAGE_MODIFIER_ID,  // 伤害修饰符ID（父类ToolItem中定义的常量）
                                baseAttackDamage + material.getAttackDamage(),  // 总伤害 = 基础值 + 材料伤害
                                EntityAttributeModifier.Operation.ADD_VALUE
                        ),
                        AttributeModifierSlot.MAINHAND  // 仅在主手生效
                )
                // 攻击速度属性
                .add(
                        EntityAttributes.GENERIC_ATTACK_SPEED,
                        new EntityAttributeModifier(
                                BASE_ATTACK_SPEED_MODIFIER_ID,  // 速度修饰符ID（父类ToolItem中定义的常量）
                                attackSpeed,  // 攻击速度（负值表示 slower，正值表示 faster）
                                EntityAttributeModifier.Operation.ADD_VALUE
                        ),
                        AttributeModifierSlot.MAINHAND  // 仅在主手生效
                )
                .build();
    }

    private static ToolComponent createToolComponent() {
        // 定义工具的特殊规则（比如对蜘蛛网的掉落概率、对剑类高效方块的挖掘效率）
        return new ToolComponent(
                // 工具规则列表：
                List.of(
                        // 规则1：破坏蜘蛛网时有15%概率直接掉落（类似剪刀）
                        ToolComponent.Rule.ofAlwaysDropping(List.of(Blocks.COBWEB), 15.0F),
                        // 规则2：对"剑类高效"标签的方块（如树叶、藤蔓）挖掘效率提升1.5倍
                        ToolComponent.Rule.of(BlockTags.SWORD_EFFICIENT, 1.5F)
                ),
                1.0F,  // 基础挖掘速度乘数（1.0为默认）
                2      // 耐久度乘数（工具实际耐久 = 材料耐久 × 此值）
        );
    }
    // 处理伤害倍数的核心逻辑
    private void handleDamageMultiplier(ItemStack stack, LivingEntity target, LivingEntity attacker) {
        // 确保攻击者是玩家（可选，非必须）
        if (!(attacker instanceof PlayerEntity player)) {
            return;
        }

        // 获取随机数生成器
        Random random = Random.create();
        float randomValue = random.nextFloat(); // 生成0-1之间的随机数

        // 获取武器的伤害倍数概率（this即当前武器，已实现接口）
        float doubleChance = this.getDoubleDamageChance();
        float tripleChance = this.getTripleDamageChance();

        // 计算原始攻击伤害（从武器属性中获取）
        float baseDamage = (float) attacker.getAttributeValue(EntityAttributes.GENERIC_ATTACK_DAMAGE);

        // 判断触发哪种倍数（注意：先判断概率低的三倍，避免被双倍覆盖）
        if (randomValue < tripleChance) {
            // 触发三倍伤害：额外造成2倍基础伤害（总伤害=3×基础伤害）
            target.damage(attacker.getDamageSources().playerAttack((PlayerEntity) attacker), baseDamage * 3);
            // 可选：发送提示（如"暴击！造成三倍伤害"）
            player.sendMessage(Text.literal("§6§l三倍伤害！"), true);
        } else if (randomValue < doubleChance + tripleChance) {
            // 触发双倍伤害：额外造成1倍基础伤害（总伤害=2×基础伤害）
            target.damage(attacker.getDamageSources().playerAttack((PlayerEntity) attacker), baseDamage * 2);
            player.sendMessage(Text.literal("§a§l双倍伤害！"), true);

        }
        else {
            // 未触发任何倍数，显示普通伤害
            player.sendMessage(Text.literal("§7§l普通伤害"), true);
        }

    }

    // 其他原有方法（createToolComponent、createAttributeModifiers等）不变...

    @Override
    public boolean canMine(BlockState state, World world, BlockPos pos, PlayerEntity miner) {
        return !miner.isCreative();
    }
    // 重写攻击命中方法，在这里触发伤害倍数逻辑
    @Override
    public boolean postHit(ItemStack stack, LivingEntity target, LivingEntity attacker) {
        // 调用父类方法（保留原有逻辑）
        super.postHit(stack, target, attacker);

        // 触发伤害倍数逻辑
        this.handleDamageMultiplier(stack, target, attacker);
        return true;
    }

    @Override
    public void postDamageEntity(ItemStack stack, LivingEntity target, LivingEntity attacker) {
        stack.damage(1, attacker, EquipmentSlot.MAINHAND);
    }

    @Override
    public void appendTooltip(ItemStack stack, TooltipContext context, List<Text> tooltip, TooltipType type) {
        super.appendTooltip(stack, context, tooltip, type);
        if (Screen.hasShiftDown()){
            tooltip.add(Text.translatable("item.shannsmod.shanns_dagger_item.shift_tooltip"));
        }
        else {
            tooltip.add(Text.translatable("item.shannsmod.shanns_dagger_item.tooltip"));
        }
    }


}
