package com.besson.shannsmod.item.custom.scythe;

import net.minecraft.client.gui.screen.Screen;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.SwordItem;
import net.minecraft.item.ToolMaterial;
import net.minecraft.item.tooltip.TooltipType;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.stat.Stats;
import net.minecraft.text.Text;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import com.besson.shannsmod.entity.ScytheSweepEntity;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ScytheItem extends SwordItem {
    // 基础属性
    private static final float BASE_DAMAGE = 1.0F;
    private static final float ATTACK_SPEED = 1.4F;
    private static final float ATTACK_RANGE = 3.5F;
    private static final float AOE_ANGLE = 120.0F;
    private static final int MAX_TARGETS = 4;
    private static final float DAMAGE_REDUCTION = 0.3F;
    private static final float LIFESTEAL_RATIO = 0.15F;
    private static final int LIFESTEAL_COOLDOWN_TICKS = 16; // 吸血冷却时间（10tick = 0.5秒）

    // 存储每个玩家的上次吸血时间（键：玩家UUID，值：游戏刻）
    private final Map<String, Integer> lastLifeStealTime = new HashMap<>();

    public ScytheItem(ToolMaterial toolMaterial, Settings settings) {
        super(toolMaterial, settings);
    }

    @Override
    public boolean postHit(ItemStack stack, LivingEntity target, LivingEntity attacker) {
        World world = attacker.getWorld();
        if (!world.isClient && attacker instanceof PlayerEntity player) {
            float damage = calculateDamage(stack, attacker);
            target.damage(world.getDamageSources().playerAttack(player), damage);

            // 尝试触发吸血（带冷却检查）
            tryApplyLifeSteal(player, damage, world.getTime());

            List<LivingEntity> aoeTargets = findAoeTargets(attacker, target);
            for (int i = 0; i < aoeTargets.size(); i++) {
                LivingEntity aoeTarget = aoeTargets.get(i);
                float multiplier = Math.max(0.1F, 1.0F - (i + 1) * DAMAGE_REDUCTION);
                float aoeDamage = damage * multiplier;
                aoeTarget.damage(world.getDamageSources().playerAttack(player), aoeDamage);

                // 对额外目标也检查冷却后吸血
                tryApplyLifeSteal(player, aoeDamage, world.getTime());
            }

            spawnSweepEffect(attacker, stack);
        }

        stack.damage(1, attacker, EquipmentSlot.MAINHAND);

        if (attacker instanceof PlayerEntity player) {
            player.incrementStat(Stats.USED.getOrCreateStat(this));
        }
        return true;
    }

    // 带冷却的吸血逻辑
    private void tryApplyLifeSteal(PlayerEntity player, float damage, long currentTime) {
        String playerId = player.getUuidAsString();
        int lastTime = lastLifeStealTime.getOrDefault(playerId, -LIFESTEAL_COOLDOWN_TICKS);

        // 检查是否超过冷却时间
        if (currentTime - lastTime >= LIFESTEAL_COOLDOWN_TICKS) {
            float heal = damage * LIFESTEAL_RATIO;
            player.heal(heal);
            lastLifeStealTime.put(playerId, (int) currentTime); // 更新上次吸血时间

            // 吸血粒子特效
            if (player.getWorld().isClient()) {
                player.getWorld().addParticle(
                        ParticleTypes.HEART,
                        player.getX(), player.getY() + 1.5, player.getZ(),
                        0, 0.1, 0
                );
            }
        }
    }

    // 其他方法保持不变...
    private float calculateDamage(ItemStack stack, LivingEntity attacker) {
        double base = attacker.getAttributeValue(EntityAttributes.GENERIC_ATTACK_DAMAGE);
        return (float) (base + BASE_DAMAGE);
    }

    private List<LivingEntity> findAoeTargets(LivingEntity attacker, LivingEntity mainTarget) {
        World world = attacker.getWorld();
        Vec3d center = attacker.getEyePos();
        return world.getEntitiesByClass(
                        LivingEntity.class,
                        attacker.getBoundingBox().expand(ATTACK_RANGE),
                        entity -> entity != attacker
                                && entity.isAlive()
                                && entity != mainTarget
                                && isInSector(attacker, entity)
                ).stream()
                .limit(MAX_TARGETS - 1)
                .collect(Collectors.toList());
    }

    private boolean isInSector(LivingEntity attacker, LivingEntity target) {
        Vec3d attackerPos = attacker.getEyePos();
        Vec3d targetPos = target.getEyePos();
        Vec3d direction = targetPos.subtract(attackerPos).normalize();
        Vec3d look = attacker.getRotationVec(1.0F);

        double dot = direction.dotProduct(look);
        double angle = Math.toDegrees(Math.acos(MathHelper.clamp(dot, -1.0, 1.0)));

        return angle <= AOE_ANGLE / 2
                && attacker.squaredDistanceTo(target) <= ATTACK_RANGE * ATTACK_RANGE;
    }

    private void spawnSweepEffect(LivingEntity attacker, ItemStack stack) {
        World world = attacker.getWorld();
        world.playSound(
                null,
                attacker.getX(), attacker.getY(), attacker.getZ(),
                SoundEvents.ENTITY_PLAYER_ATTACK_SWEEP,
                SoundCategory.PLAYERS,
                1.0F,
                0.9F + world.random.nextFloat() * 0.2F
        );
        ScytheSweepEntity effect = new ScytheSweepEntity(world, attacker);
        world.spawnEntity(effect);
    }

    @Override
    public void appendTooltip(ItemStack stack, TooltipContext context, List<Text> tooltip, TooltipType type) {
        super.appendTooltip(stack, context, tooltip, type);
        if (Screen.hasShiftDown()){
            tooltip.add(Text.translatable("item.shannsmod.shann_scythe_item.shift_tooltip"));
        }
        else {
            tooltip.add(Text.translatable("item.shannsmod.shann_scythe_item.tooltip"));
        }
    }
}

