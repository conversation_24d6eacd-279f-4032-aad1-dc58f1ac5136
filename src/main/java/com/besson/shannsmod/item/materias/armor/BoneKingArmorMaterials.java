package com.besson.shannsmod.item.materias.armor;

import com.besson.shannsmod.ShannsMod;
import com.besson.shannsmod.item.custom.armor.BoneKingArmor;
import com.google.common.collect.Maps;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.ArmorMaterial;
import net.minecraft.item.Items;
import net.minecraft.recipe.Ingredient;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.Identifier;
import net.minecraft.util.Util;

import java.util.List;
import java.util.function.Supplier;

public class BoneKingArmorMaterials {
    public static final RegistryEntry<ArmorMaterial> BONE_KING_MATE = register();

    private static RegistryEntry<ArmorMaterial> register() {
        final var enumMap = Util.make(Maps.<ArmorItem.Type, Integer>newEnumMap(ArmorItem.Type.class), it -> {
            it.put(BoneKingArmor.Type.BOOTS, (int)4);
            it.put(BoneKingArmor.Type.LEGGINGS, (int)6);
            it.put(BoneKingArmor.Type.CHESTPLATE, (int)8);
            it.put(BoneKingArmor.Type.HELMET, (int)4);
            it.put(BoneKingArmor.Type.BODY, 12);
        });

        final var rl = Identifier.of(ShannsMod.MOD_ID, "bone_king");

        final var list = List.of(new ArmorMaterial.Layer(rl));
        final Supplier<Ingredient> supplier = () -> Ingredient.ofItems(Items.NETHERITE_INGOT);

        return Registry.registerReference(Registries.ARMOR_MATERIAL, rl,
                new ArmorMaterial(enumMap, 15, SoundEvents.ITEM_ARMOR_EQUIP_NETHERITE, supplier, list, (float) 3.5, (float) 0.15));
    }
}
