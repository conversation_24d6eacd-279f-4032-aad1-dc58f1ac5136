package com.besson.shannsmod.item;

import com.besson.shannsmod.ShannsMod;
import net.minecraft.item.ItemGroup;
import net.minecraft.item.ItemGroups;
import net.minecraft.item.ItemStack;
import net.minecraft.registry.*;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import net.minecraft.registry.RegistryKey; // 新增导入
import net.minecraft.registry.RegistryKeys; // 新增导入


public class ModItemGroups {
//    public static final RegistryKey<ItemGroup> SHANNS_GROUP = register("shanns_group");
//    private static RegistryKey<ItemGroup>register(String id){
//        return RegistryKey.of(RegistryKeys.ITEM_GROUP, Identifier.of(ShannsMod.MOD_ID, id));
//    }
//    public static void registerModItemGroups(){
//        Registry.register(Registries.ITEM_GROUP,SHANNS_GROUP,
//                ItemGroup.create(ItemGroup.Row.TOP,7)
//                    .displayName(Text.translatable("itemGroup.shanns_group"))
//                            .icon(() -> new ItemStack(ModItems.HALLOWEEN_PICKAXE))
//                                    .entries((displayContext, entries) -> {
//                                        entries.add(ModItems.HALLOWEEN_PICKAXE);
//                                    }).build());
//        ShannsMod.LOGGER.info("Registering Item Groups");
//    }
    public static final ItemGroup SHANNS_GROUP = Registry.register(Registries.ITEM_GROUP, Identifier.of(ShannsMod.MOD_ID,"shanns_group"),
        ItemGroup.create(null,-1).displayName(Text.translatable("itemGroup.shanns_group"))
                .icon(() -> new ItemStack(ModItems.GROUDON_SWORD))
                .entries((displayContext, entries) -> {
                    entries.add(ModItems.HALLOWEEN_DEBRIS);//物品

                    entries.add(ModItems.GROUDON_SWORD);//剑
                    entries.add(ModItems.DIALGA_SWORD);
                    entries.add(ModItems.KINGDOM_SWORD);
                    entries.add(ModItems.BARBARIANS_SWORD);
                    entries.add(ModItems.DEMON_LORD_SWORD);
                    entries.add(ModItems.SLIME_SWORD);
                    entries.add(ModItems.EXILE_SWORD);
                    entries.add(ModItems.CRYSTALLINE_WESTERN_SWORD);
                    entries.add(ModItems.PURPLE_CRYSTALLINE_WESTERN_SWORD);
                    entries.add(ModItems.SPRING_SWORD);
                    entries.add(ModItems.SUMMER_SWORD);
                    entries.add(ModItems.AUTUMN_SWORD);
                    entries.add(ModItems.WINTER_SWORD);
                    entries.add(ModItems.CELEBRATION_SWORD);
                    entries.add(ModItems.HALLOWEEN_SWORD);
                    entries.add(ModItems.TENTACLE_SWORD);

                    entries.add(ModItems.DEMON_LORD_SCYTHE);//镰刀
                    entries.add(ModItems.FARMER_SCYTHE);
                    entries.add(ModItems.DEATH_SCYTHE);

                    entries.add(ModItems.STARFISH_DAGGER);//匕首
                    entries.add(ModItems.KINGDOM_DAGGER);
                    entries.add(ModItems.BARBARIANS_DAGGER);
                    entries.add(ModItems.SHADOW_ASSASSIN_DAGGER);
                    entries.add(ModItems.ARCANE_DAGGER);
                    entries.add(ModItems.MEGA_ARCANE_DAGGER);
                    entries.add(ModItems.BUTCHER_DAGGER);
                    entries.add(ModItems.SPRING_DAGGER);
                    entries.add(ModItems.SUMMER_DAGGER);
                    entries.add(ModItems.AUTUMN_DAGGER);
                    entries.add(ModItems.WINTER_DAGGER);
                    entries.add(ModItems.CELEBRATION_DAGGER);

                    entries.add(ModItems.KINGDOM_BOW);//弓
                    entries.add(ModItems.BARBARIANS_BOW);
                    entries.add(ModItems.DEMON_LORD_BOW);
                    entries.add(ModItems.SHADOW_ASSASSIN_BOW);
                    entries.add(ModItems.SPRING_BOW);
                    entries.add(ModItems.SUMMER_BOW);
                    entries.add(ModItems.AUTUMN_BOW);
                    entries.add(ModItems.WINTER_BOW);
                    entries.add(ModItems.HALLOWEEN_BOW);
                    entries.add(ModItems.SAKURA_BOW);

                    entries.add(ModItems.KYOGRE_SHIELD);//盾
                    entries.add(ModItems.KINGDOM_SHIELD);
                    entries.add(ModItems.BARBARIANS_SHIELD);
                    entries.add(ModItems.DEMON_LORD_SHIELD);
                    entries.add(ModItems.SPRING_SHIELD);
                    entries.add(ModItems.SUMMER_SHIELD);
                    entries.add(ModItems.AUTUMN_SHIELD);
                    entries.add(ModItems.WINTER_SHIELD);
                    entries.add(ModItems.HALLOWEEN_SHIELD);

                    entries.add(ModItems.KINGDOM_PICKAXE);//镐子
                    entries.add(ModItems.BARBARIAN_PICKAXE);
                    entries.add(ModItems.DEMON_LORD_PICKAXE);
                    entries.add(ModItems.SPRING_PICKAXE);
                    entries.add(ModItems.SUMMER_PICKAXE);
                    entries.add(ModItems.AUTUMN_PICKAXE);
                    entries.add(ModItems.WINTER_PICKAXE);
                    entries.add(ModItems.HALLOWEEN_PICKAXE);

                    entries.add(ModItems.SHANN_HELMET);//头饰
                    entries.add(ModItems.STRAW_HAT);
                    entries.add(ModItems.HALLOWEEN_PUMPKIN_HELMET);

                    entries.add(ModItems.SHADOW_ASSASSIN_HELMET);//盔甲
                    entries.add(ModItems.SHADOW_ASSASSIN_CHESTPLATE);
                    entries.add(ModItems.SHADOW_ASSASSIN_LEGGINGS);
                    entries.add(ModItems.SHADOW_ASSASSIN_BOOTS);
                    entries.add(ModItems.ARCANE_HELMET);
                    entries.add(ModItems.ARCANE_CHESTPLATE);
                    entries.add(ModItems.ARCANE_LEGGINGS);
                    entries.add(ModItems.ARCANE_BOOTS);
                    entries.add(ModItems.DEMON_LORD_HELMET);
                    entries.add(ModItems.DEMON_LORD_CHESTPLATE);
                    entries.add(ModItems.DEMON_LORD_LEGGINGS);
                    entries.add(ModItems.DEMON_LORD_BOOTS);
                    entries.add(ModItems.SPRING_HELMET);
                    entries.add(ModItems.SPRING_CHESTPLATE);
                    entries.add(ModItems.SPRING_LEGGINGS);
                    entries.add(ModItems.SPRING_BOOTS);
                    entries.add(ModItems.SUMMER_HELMET);
                    entries.add(ModItems.SUMMER_CHESTPLATE);
                    entries.add(ModItems.SUMMER_LEGGINGS);
                    entries.add(ModItems.SUMMER_BOOTS);
                    entries.add(ModItems.AUTUMN_HELMET);
                    entries.add(ModItems.AUTUMN_CHESTPLATE);
                    entries.add(ModItems.AUTUMN_LEGGINGS);
                    entries.add(ModItems.AUTUMN_BOOTS);
                    entries.add(ModItems.WINTER_HELMET);
                    entries.add(ModItems.WINTER_CHESTPLATE);
                    entries.add(ModItems.WINTER_LEGGINGS);
                    entries.add(ModItems.WINTER_BOOTS);
                    entries.add(ModItems.CELEBRATION_HELMET);
                    entries.add(ModItems.CELEBRATION_CHESTPLATE);
                    entries.add(ModItems.CELEBRATION_LEGGINGS);
                    entries.add(ModItems.CELEBRATION_BOOTS);
                    entries.add(ModItems.BONE_KING_HELMET);
                    entries.add(ModItems.BONE_KING_CHESTPLATE);
                    entries.add(ModItems.BONE_KING_LEGGINGS);
                    entries.add(ModItems.BONE_KING_BOOTS);
                    entries.add(ModItems.CLOWN_HELMET);

                    entries.add(ModItems.TERROR_XPP_SPAWN_EGG);//刷怪蛋


                }).build());
    public static void registerModItemGroups(){
        ShannsMod.LOGGER.info("Registering Item Groups");
    }
}
