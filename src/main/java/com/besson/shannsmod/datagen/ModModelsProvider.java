package com.besson.shannsmod.datagen;

import com.besson.shannsmod.item.ModItems;
import net.fabricmc.fabric.api.datagen.v1.FabricDataOutput;
import net.fabricmc.fabric.api.datagen.v1.provider.FabricModelProvider;
import net.minecraft.data.client.BlockStateModelGenerator;
import net.minecraft.data.client.ItemModelGenerator;
import net.minecraft.data.client.Models;
import net.minecraft.item.ArmorItem;

public class ModModelsProvider extends FabricModelProvider {
    public ModModelsProvider(FabricDataOutput output) {
        super(output);
    }

    @Override
    public void generateBlockStateModels(BlockStateModelGenerator blockStateModelGenerator) {

    }

    @Override
    public void generateItemModels(ItemModelGenerator itemModelGenerator) {
        itemModelGenerator.register(ModItems.HALLOWEEN_DEBRIS, Models.GENERATED);
        itemModelGenerator.register(ModItems.HALLOWEEN_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.HALLOWEEN_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.HALLOWEEN_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.HALLOWEEN_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.HALLOWEEN_PUMPKIN_HELMET, Models.HANDHELD);
        itemModelGenerator.register(ModItems.GROUDON_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KINGDOM_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.BARBARIANS_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KINGDOM_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.BARBARIANS_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KYOGRE_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.BARBARIANS_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KINGDOM_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SLIME_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KINGDOM_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SHANN_HELMET, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KINGDOM_TRIDENT, Models.HANDHELD);
        itemModelGenerator.register(ModItems.BARBARIAN_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.STARFISH_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.KINGDOM_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.BARBARIANS_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SPRING_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SUMMER_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.AUTUMN_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.WINTER_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SPRING_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SUMMER_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.AUTUMN_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.WINTER_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SPRING_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SUMMER_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.AUTUMN_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.WINTER_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SPRING_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SUMMER_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.AUTUMN_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.WINTER_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SPRING_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SUMMER_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.AUTUMN_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.WINTER_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SAKURA_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.ARCANE_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.MEGA_ARCANE_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.CRYSTALLINE_WESTERN_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.PURPLE_CRYSTALLINE_WESTERN_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.BUTCHER_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.CELEBRATION_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.CELEBRATION_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.STRAW_HAT, Models.HANDHELD);
        itemModelGenerator.register(ModItems.TENTACLE_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DIALGA_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.EXILE_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DEMON_LORD_SCYTHE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DEMON_LORD_SWORD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DEMON_LORD_SHIELD, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DEMON_LORD_BOW, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DEMON_LORD_PICKAXE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.FARMER_SCYTHE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.DEATH_SCYTHE, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SHADOW_ASSASSIN_DAGGER, Models.HANDHELD);
        itemModelGenerator.register(ModItems.SHADOW_ASSASSIN_BOW, Models.HANDHELD);



        itemModelGenerator.registerArmor((ArmorItem) ModItems.ARCANE_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.ARCANE_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.ARCANE_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.ARCANE_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.CELEBRATION_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.CELEBRATION_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.CELEBRATION_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.CELEBRATION_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SPRING_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SPRING_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SPRING_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SPRING_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SUMMER_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SUMMER_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SUMMER_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SUMMER_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.AUTUMN_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.AUTUMN_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.AUTUMN_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.AUTUMN_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.WINTER_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.WINTER_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.WINTER_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.WINTER_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.CLOWN_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.BONE_KING_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.BONE_KING_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.BONE_KING_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.BONE_KING_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.DEMON_LORD_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.DEMON_LORD_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.DEMON_LORD_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.DEMON_LORD_BOOTS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SHADOW_ASSASSIN_HELMET);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SHADOW_ASSASSIN_CHESTPLATE);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SHADOW_ASSASSIN_LEGGINGS);
        itemModelGenerator.registerArmor((ArmorItem) ModItems.SHADOW_ASSASSIN_BOOTS);

    }
}
