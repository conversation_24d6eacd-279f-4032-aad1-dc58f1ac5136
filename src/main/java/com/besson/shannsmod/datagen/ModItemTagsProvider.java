package com.besson.shannsmod.datagen;

import com.besson.shannsmod.item.ModItems;
import net.fabricmc.fabric.api.datagen.v1.FabricDataOutput;
import net.fabricmc.fabric.api.datagen.v1.provider.FabricTagProvider;
import net.minecraft.registry.RegistryWrapper;
import net.minecraft.registry.tag.ItemTags;
import org.jetbrains.annotations.Nullable;

import java.util.concurrent.CompletableFuture;

public class ModItemTagsProvider extends FabricTagProvider.ItemTagProvider {
    // 修复构造函数：移除 BlockTagProvider 参数
    public ModItemTagsProvider(FabricDataOutput output, CompletableFuture<RegistryWrapper.WrapperLookup> completableFuture) {
        super(output, completableFuture);
    }

    @Override
    protected void configure(RegistryWrapper.WrapperLookup wrapperLookup) {
        getOrCreateTagBuilder(ItemTags.TRIMMABLE_ARMOR)
                .add(ModItems.SPRING_HELMET)
                .add(ModItems.SPRING_CHESTPLATE)
                .add(ModItems.SPRING_LEGGINGS)
                .add(ModItems.SPRING_BOOTS)
                .add(ModItems.SUMMER_HELMET)
                .add(ModItems.SUMMER_CHESTPLATE)
                .add(ModItems.SUMMER_LEGGINGS)
                .add(ModItems.SUMMER_BOOTS)
                .add(ModItems.AUTUMN_HELMET)
                .add(ModItems.AUTUMN_CHESTPLATE)
                .add(ModItems.AUTUMN_LEGGINGS)
                .add(ModItems.AUTUMN_BOOTS)
                .add(ModItems.WINTER_HELMET)
                .add(ModItems.WINTER_CHESTPLATE)
                .add(ModItems.WINTER_LEGGINGS)
                .add(ModItems.WINTER_BOOTS)
                .add(ModItems.CELEBRATION_HELMET)
                .add(ModItems.CELEBRATION_CHESTPLATE)
                .add(ModItems.CELEBRATION_LEGGINGS)
                .add(ModItems.CELEBRATION_BOOTS)
                .add(ModItems.BONE_KING_HELMET)
                .add(ModItems.BONE_KING_CHESTPLATE)
                .add(ModItems.BONE_KING_LEGGINGS)
                .add(ModItems.BONE_KING_BOOTS)
                .add(ModItems.SHANN_HELMET)
                .add(ModItems.STRAW_HAT)
                .add(ModItems.HALLOWEEN_PUMPKIN_HELMET);

        getOrCreateTagBuilder(ItemTags.SWORD_ENCHANTABLE)
                .add(ModItems.HALLOWEEN_SWORD)
                .add(ModItems.GROUDON_SWORD)
                .add(ModItems.DIALGA_SWORD)
                .add(ModItems.KINGDOM_SWORD)
                .add(ModItems.BARBARIANS_SWORD)
                .add(ModItems.CRYSTALLINE_WESTERN_SWORD)
                .add(ModItems.PURPLE_CRYSTALLINE_WESTERN_SWORD)
                .add(ModItems.KINGDOM_DAGGER)
                .add(ModItems.BARBARIANS_DAGGER)
                .add(ModItems.BUTCHER_DAGGER)
                .add(ModItems.STARFISH_DAGGER)
                .add(ModItems.ARCANE_DAGGER)
                .add(ModItems.MEGA_ARCANE_DAGGER)
                .add(ModItems.SPRING_DAGGER)
                .add(ModItems.SUMMER_DAGGER)
                .add(ModItems.AUTUMN_DAGGER)
                .add(ModItems.WINTER_DAGGER)
                .add(ModItems.SPRING_SWORD)
                .add(ModItems.SUMMER_SWORD)
                .add(ModItems.AUTUMN_SWORD)
                .add(ModItems.WINTER_SWORD)
                .add(ModItems.CELEBRATION_SWORD)
                .add(ModItems.CELEBRATION_DAGGER)
                .add(ModItems.TENTACLE_SWORD)
                .add(ModItems.EXILE_SWORD)
                .add(ModItems.DEMON_LORD_SWORD)
                .add(ModItems.SHADOW_ASSASSIN_DAGGER)
                .add(ModItems.SLIME_SWORD);

        getOrCreateTagBuilder(ItemTags.SHARP_WEAPON_ENCHANTABLE)
                .add(ModItems.HALLOWEEN_SWORD)
                .add(ModItems.GROUDON_SWORD)
                .add(ModItems.DIALGA_SWORD)
                .add(ModItems.KINGDOM_SWORD)
                .add(ModItems.BARBARIANS_SWORD)
                .add(ModItems.CRYSTALLINE_WESTERN_SWORD)
                .add(ModItems.PURPLE_CRYSTALLINE_WESTERN_SWORD)
                .add(ModItems.KINGDOM_DAGGER)
                .add(ModItems.BARBARIANS_DAGGER)
                .add(ModItems.BUTCHER_DAGGER)
                .add(ModItems.STARFISH_DAGGER)
                .add(ModItems.ARCANE_DAGGER)
                .add(ModItems.MEGA_ARCANE_DAGGER)
                .add(ModItems.SPRING_DAGGER)
                .add(ModItems.SUMMER_DAGGER)
                .add(ModItems.AUTUMN_DAGGER)
                .add(ModItems.WINTER_DAGGER)
                .add(ModItems.SPRING_SWORD)
                .add(ModItems.SUMMER_SWORD)
                .add(ModItems.AUTUMN_SWORD)
                .add(ModItems.WINTER_SWORD)
                .add(ModItems.CELEBRATION_SWORD)
                .add(ModItems.CELEBRATION_DAGGER)
                .add(ModItems.TENTACLE_SWORD)
                .add(ModItems.EXILE_SWORD)
                .add(ModItems.SLIME_SWORD)
                .add(ModItems.DEMON_LORD_SWORD)
                .add(ModItems.FARMER_SCYTHE)
                .add(ModItems.DEATH_SCYTHE)
                .add(ModItems.SHADOW_ASSASSIN_DAGGER)
                .add(ModItems.DEMON_LORD_SCYTHE);

        getOrCreateTagBuilder(ItemTags.BOW_ENCHANTABLE)
                .add(ModItems.SAKURA_BOW)
                .add(ModItems.HALLOWEEN_BOW)
                .add(ModItems.KINGDOM_BOW)
                .add(ModItems.SPRING_BOW)
                .add(ModItems.SUMMER_BOW)
                .add(ModItems.AUTUMN_BOW)
                .add(ModItems.WINTER_BOW)
                .add(ModItems.DEMON_LORD_BOW)
                .add(ModItems.SHADOW_ASSASSIN_BOW)
                .add(ModItems.BARBARIANS_BOW);

        getOrCreateTagBuilder(ItemTags.MINING_ENCHANTABLE)
                .add(ModItems.KINGDOM_PICKAXE)
                .add(ModItems.SPRING_PICKAXE)
                .add(ModItems.SUMMER_PICKAXE)
                .add(ModItems.AUTUMN_PICKAXE)
                .add(ModItems.WINTER_PICKAXE)
                .add(ModItems.DEMON_LORD_PICKAXE)
                .add(ModItems.HALLOWEEN_PICKAXE);

        getOrCreateTagBuilder(ItemTags.MINING_LOOT_ENCHANTABLE)
                .add(ModItems.KINGDOM_PICKAXE)
                .add(ModItems.SPRING_PICKAXE)
                .add(ModItems.SUMMER_PICKAXE)
                .add(ModItems.AUTUMN_PICKAXE)
                .add(ModItems.WINTER_PICKAXE)
                .add(ModItems.DEMON_LORD_PICKAXE)
                .add(ModItems.HALLOWEEN_PICKAXE);

        getOrCreateTagBuilder(ItemTags.ARMOR_ENCHANTABLE)
                .add(ModItems.ARCANE_HELMET)
                .add(ModItems.ARCANE_CHESTPLATE)
                .add(ModItems.ARCANE_LEGGINGS)
                .add(ModItems.ARCANE_BOOTS)
                .add(ModItems.CELEBRATION_HELMET)
                .add(ModItems.CELEBRATION_CHESTPLATE)
                .add(ModItems.CELEBRATION_LEGGINGS)
                .add(ModItems.CELEBRATION_BOOTS)
                .add(ModItems.SPRING_HELMET)
                .add(ModItems.SPRING_CHESTPLATE)
                .add(ModItems.SPRING_LEGGINGS)
                .add(ModItems.SPRING_BOOTS)
                .add(ModItems.SUMMER_HELMET)
                .add(ModItems.SUMMER_CHESTPLATE)
                .add(ModItems.SUMMER_LEGGINGS)
                .add(ModItems.SUMMER_BOOTS)
                .add(ModItems.AUTUMN_HELMET)
                .add(ModItems.AUTUMN_CHESTPLATE)
                .add(ModItems.AUTUMN_LEGGINGS)
                .add(ModItems.AUTUMN_BOOTS)
                .add(ModItems.WINTER_HELMET)
                .add(ModItems.WINTER_CHESTPLATE)
                .add(ModItems.WINTER_LEGGINGS)
                .add(ModItems.WINTER_BOOTS)
                .add(ModItems.CLOWN_HELMET)
                .add(ModItems.BONE_KING_HELMET)
                .add(ModItems.BONE_KING_CHESTPLATE)
                .add(ModItems.BONE_KING_LEGGINGS)
                .add(ModItems.BONE_KING_BOOTS)
                .add(ModItems.DEMON_LORD_HELMET)
                .add(ModItems.DEMON_LORD_CHESTPLATE)
                .add(ModItems.DEMON_LORD_LEGGINGS)
                .add(ModItems.DEMON_LORD_BOOTS)
                .add(ModItems.HALLOWEEN_PUMPKIN_HELMET)
                .add(ModItems.STRAW_HAT)
                .add(ModItems.SHADOW_ASSASSIN_HELMET)
                .add(ModItems.SHADOW_ASSASSIN_CHESTPLATE)
                .add(ModItems.SHADOW_ASSASSIN_LEGGINGS)
                .add(ModItems.SHADOW_ASSASSIN_BOOTS)
                .add(ModItems.SHANN_HELMET);



    }
}

