package com.besson.shannsmod.datagen;

import com.besson.shannsmod.entity.ModEntities;
import com.besson.shannsmod.item.ModItems;
import net.fabricmc.fabric.api.datagen.v1.FabricDataOutput;
import net.fabricmc.fabric.api.datagen.v1.provider.FabricLanguageProvider;
import net.minecraft.item.ItemGroups;
import net.minecraft.registry.RegistryWrapper;

import java.util.concurrent.CompletableFuture;

public class ModENUSlanProvider extends FabricLanguageProvider {
    public ModENUSlanProvider(FabricDataOutput dataOutput, CompletableFuture<RegistryWrapper.WrapperLookup> registryLookup) {
        super(dataOutput,"zh_cn" , registryLookup);
    }

    @Override
    public void generateTranslations(RegistryWrapper.WrapperLookup wrapperLookup, TranslationBuilder translationBuilder) {
        translationBuilder.add(ModItems.HALLOWEEN_DEBRIS,"§f紞§a万圣节碎片");
        translationBuilder.add(ModItems.HALLOWEEN_PICKAXE,"§f紞§d万圣节镐");
        translationBuilder.add(ModItems.HALLOWEEN_SWORD,"§f紞§d万圣节刀");
        translationBuilder.add(ModItems.HALLOWEEN_BOW,"§f紞§d万圣节弓");
        translationBuilder.add(ModItems.HALLOWEEN_SHIELD,"§f紞§d万圣节盾");
        translationBuilder.add(ModItems.HALLOWEEN_PUMPKIN_HELMET,"§f紞§d万圣节南瓜头饰");
        translationBuilder.add(ModItems.SHANN_HELMET,"§f紞§dshann_xpp头饰");
        translationBuilder.add(ModItems.CELEBRATION_HELMET,"§f拲§d周年庆头盔");
        translationBuilder.add(ModItems.CELEBRATION_CHESTPLATE,"§f拲§d周年庆胸甲");
        translationBuilder.add(ModItems.CELEBRATION_LEGGINGS,"§f拲§d周年庆护腿");
        translationBuilder.add(ModItems.CELEBRATION_BOOTS,"§f拲§d周年庆靴子");
        translationBuilder.add(ModItems.DEATH_SCYTHE,"§f紞§d死神镰刀");
        translationBuilder.add(ModItems.CLOWN_HELMET,"§f紞§d潘尼怀特头盔");
        translationBuilder.add(ModItems.BONE_KING_HELMET,"§f紞§d骷髅王头盔");
        translationBuilder.add(ModItems.BONE_KING_CHESTPLATE,"§f紞§d骷髅王胸甲");
        translationBuilder.add(ModItems.BONE_KING_LEGGINGS,"§f紞§d骷髅王护腿");
        translationBuilder.add(ModItems.BONE_KING_BOOTS,"§f紞§d骷髅王靴子");
        translationBuilder.add(ModItems.GROUDON_SWORD,"§f拲§c§l◆大地之剑");
        translationBuilder.add(ModItems.KYOGRE_SHIELD,"§f拲§c§l◆海洋之盾");
        translationBuilder.add(ModItems.DIALGA_SWORD,"§f拲§c§l◆时间之刃");
        translationBuilder.add(ModItems.EXILE_SWORD,"§f拲§6流亡之刃");
        translationBuilder.add(ModItems.KINGDOM_SWORD,"§f拲§b帝国剑");
        translationBuilder.add(ModItems.BARBARIANS_SWORD,"§f拲§b蛮族剑");
        translationBuilder.add(ModItems.KINGDOM_BOW,"§f拲§b帝国弓");
        translationBuilder.add(ModItems.BARBARIANS_BOW,"§f拲§b蛮族弓");
        translationBuilder.add(ModItems.KINGDOM_SHIELD,"§f拲§b帝国盾");
        translationBuilder.add(ModItems.BARBARIANS_SHIELD,"§f拲§b蛮族盾");
        translationBuilder.add(ModItems.KINGDOM_PICKAXE,"§f拲§b帝国镐");
        translationBuilder.add(ModItems.BARBARIAN_PICKAXE,"§f拲§b蛮族镐");
        translationBuilder.add(ModItems.STARFISH_DAGGER,"§f拲§6血肉匕首");
        translationBuilder.add(ModItems.KINGDOM_DAGGER,"§f拲§b帝国匕首");
        translationBuilder.add(ModItems.BARBARIANS_DAGGER,"§f拲§b蛮族匕首");
        translationBuilder.add(ModItems.SPRING_DAGGER,"§f拲§b绿意爪");
        translationBuilder.add(ModItems.SUMMER_DAGGER,"§f拲§b炎热爪");
        translationBuilder.add(ModItems.AUTUMN_DAGGER,"§f拲§b丰收爪");
        translationBuilder.add(ModItems.WINTER_DAGGER,"§f拲§b寒冬爪");
        translationBuilder.add(ModItems.SPRING_SWORD,"§f拲§b绿意剑");
        translationBuilder.add(ModItems.SUMMER_SWORD,"§f拲§b炎热剑");
        translationBuilder.add(ModItems.AUTUMN_SWORD,"§f拲§b丰收剑");
        translationBuilder.add(ModItems.WINTER_SWORD,"§f拲§b寒冬剑");
        translationBuilder.add(ModItems.SPRING_PICKAXE,"§f拲§b绿意镐");
        translationBuilder.add(ModItems.SUMMER_PICKAXE,"§f拲§b炎热镐");
        translationBuilder.add(ModItems.AUTUMN_PICKAXE,"§f拲§b丰收镐");
        translationBuilder.add(ModItems.WINTER_PICKAXE,"§f拲§b寒冬镐");
        translationBuilder.add(ModItems.SPRING_SHIELD,"§f拲§b绿意盾");
        translationBuilder.add(ModItems.SUMMER_SHIELD,"§f拲§b炎热盾");
        translationBuilder.add(ModItems.AUTUMN_SHIELD,"§f拲§b丰收盾");
        translationBuilder.add(ModItems.WINTER_SHIELD,"§f拲§b寒冬盾");
        translationBuilder.add(ModItems.SPRING_BOW,"§f拲§b绿意弓");
        translationBuilder.add(ModItems.SUMMER_BOW,"§f拲§b炎热弓");
        translationBuilder.add(ModItems.AUTUMN_BOW,"§f拲§b丰收弓");
        translationBuilder.add(ModItems.WINTER_BOW,"§f拲§b寒冬弓");
        translationBuilder.add(ModItems.SPRING_HELMET,"§f拲§b绿意头盔");
        translationBuilder.add(ModItems.SPRING_CHESTPLATE,"§f拲§b绿意胸甲");
        translationBuilder.add(ModItems.SPRING_LEGGINGS,"§f拲§b绿意护腿");
        translationBuilder.add(ModItems.SPRING_BOOTS,"§f拲§b绿意靴子");
        translationBuilder.add(ModItems.SUMMER_HELMET,"§f拲§b炎热头盔");
        translationBuilder.add(ModItems.SUMMER_CHESTPLATE,"§f拲§b炎热胸甲");
        translationBuilder.add(ModItems.SUMMER_LEGGINGS,"§f拲§b炎热护腿");
        translationBuilder.add(ModItems.SUMMER_BOOTS,"§f拲§b炎热靴子");
        translationBuilder.add(ModItems.AUTUMN_HELMET,"§f拲§b丰收头盔");
        translationBuilder.add(ModItems.AUTUMN_CHESTPLATE,"§f拲§b丰收盔甲");
        translationBuilder.add(ModItems.AUTUMN_LEGGINGS,"§f拲§b丰收护腿");
        translationBuilder.add(ModItems.AUTUMN_BOOTS,"§f拲§b丰收靴子");
        translationBuilder.add(ModItems.WINTER_HELMET,"§f拲§b寒冬头盔");
        translationBuilder.add(ModItems.WINTER_CHESTPLATE,"§f拲§b寒冬胸甲");
        translationBuilder.add(ModItems.WINTER_LEGGINGS,"§f拲§b寒冬护腿");
        translationBuilder.add(ModItems.WINTER_BOOTS,"§f拲§b寒冬靴子");
        translationBuilder.add(ModItems.DEMON_LORD_HELMET,"§f拲§6恶魔领主头盔");
        translationBuilder.add(ModItems.DEMON_LORD_CHESTPLATE,"§f拲§6恶魔领主胸甲");
        translationBuilder.add(ModItems.DEMON_LORD_LEGGINGS,"§f拲§6恶魔领主护腿");
        translationBuilder.add(ModItems.DEMON_LORD_BOOTS,"§f拲§6恶魔领主靴子");
        translationBuilder.add(ModItems.DEMON_LORD_SCYTHE,"§f拲§6恶魔领主之镰");
        translationBuilder.add(ModItems.DEMON_LORD_SWORD,"§f拲§6恶魔领主之剑");
        translationBuilder.add(ModItems.DEMON_LORD_SHIELD,"§f拲§6恶魔领主之盾");
        translationBuilder.add(ModItems.DEMON_LORD_BOW,"§f拲§6恶魔领主之弓");
        translationBuilder.add(ModItems.DEMON_LORD_PICKAXE,"§f拲§6恶魔领主之镐");
        translationBuilder.add(ModItems.FARMER_SCYTHE,"§f拲§b农夫镰刀");
        translationBuilder.add(ModItems.SLIME_SWORD,"§f拲§b史莱姆剑");
        translationBuilder.add(ModItems.ARCANE_DAGGER,"§f拲§b奥术匕首");
        translationBuilder.add(ModItems.MEGA_ARCANE_DAGGER,"§f拲§6mega奥术匕首");
        translationBuilder.add(ModItems.BUTCHER_DAGGER,"§f拲§b屠夫匕首");
        translationBuilder.add(ModItems.CRYSTALLINE_WESTERN_SWORD,"§f拲§b结晶西洋剑");
        translationBuilder.add(ModItems.PURPLE_CRYSTALLINE_WESTERN_SWORD,"§f拲§6紫结晶西洋剑");
        translationBuilder.add(ModItems.CELEBRATION_SWORD,"§f紞§d周年庆剑");
        translationBuilder.add(ModItems.CELEBRATION_DAGGER,"§f紞§d周年庆匕首");
        translationBuilder.add(ModItems.SAKURA_BOW,"§f拲§6樱花弓");
        translationBuilder.add(ModItems.ARCANE_HELMET,"§f拲§b奥术头盔");
        translationBuilder.add(ModItems.ARCANE_CHESTPLATE,"§f拲§b奥术胸甲");
        translationBuilder.add(ModItems.ARCANE_LEGGINGS,"§f拲§b奥术护腿");
        translationBuilder.add(ModItems.ARCANE_BOOTS,"§f拲§b奥术靴子");
        translationBuilder.add(ModItems.SHADOW_ASSASSIN_HELMET,"§f拲§b影子刺客头盔");
        translationBuilder.add(ModItems.SHADOW_ASSASSIN_CHESTPLATE,"§f拲§b影子刺客胸甲");
        translationBuilder.add(ModItems.SHADOW_ASSASSIN_LEGGINGS,"§f拲§b影子刺客护腿");
        translationBuilder.add(ModItems.SHADOW_ASSASSIN_BOOTS,"§f拲§b影子刺客靴子");
        translationBuilder.add(ModItems.STRAW_HAT,"§f拲§b农夫草帽");
        translationBuilder.add(ModItems.TENTACLE_SWORD,"§f拲§6触手刀");
        translationBuilder.add(ModItems.SHADOW_ASSASSIN_DAGGER,"§f拲§b影子刺客匕首");
        translationBuilder.add(ModItems.SHADOW_ASSASSIN_BOW,"§f拲§b影子刺客弓");
        translationBuilder.add(ModItems.TERROR_XPP_SPAWN_EGG,"§f拲§d恐怖XPP刷怪蛋");
        translationBuilder.add(ModEntities.TERROR_XPP,"§d恐怖XPP");
        translationBuilder.add("itemGroup.shanns_group","§6Shann_xpp制作的小牛物语服务器模组");
        translationBuilder.add("item.shannsmod.barbarians_bow.shift_tooltip","§c蛮族用的黑铁大弓\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.barbarians_bow.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.kingdom_bow.shift_tooltip","§c皇家用的金银质弓\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.kingdom_bow.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.halloween_pumpkin_helmet.shift_tooltip","§c完蛋我被南瓜包围了\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.halloween_pumpkin_helmet.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.halloween_bow.shift_tooltip","§c用来吓死你的弓\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.halloween_bow.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shann_helmet.shift_tooltip","§c让你看起来像开发者\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shann_helmet.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shanns_sword_item.shift_tooltip","§c剑类武器\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shanns_sword_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shanns_dagger_item.shift_tooltip","§c匕首类武器\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shanns_dagger_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shanns_bow_item.shift_tooltip","§c弓类武器\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shanns_bow_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.starfish_dagger_item.shift_tooltip","§c让你获得开发者的力量...那么...代价是什么...\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.starfish_dagger_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.sakura_bow_item.shift_tooltip","§c管理员制作\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.sakura_bow_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shanns_op_sword_item.shift_tooltip","§c管理员制作\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shanns_op_sword_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shann_scythe_item.shift_tooltip","§c镰刀类武器\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shann_scythe_item.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.arcane_armor.shift_tooltip","§c奥术师的装备\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.arcane_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.bone_king_armor.shift_tooltip","§c传说骷髅王使用的盔甲\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.bone_king_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.clown_armor.shift_tooltip","§c含有潘尼怀特灵魂的头\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.clown_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.celebration_armor.shift_tooltip","§c小牛物语周年庆！\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.celebration_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.demon_lord_armor.shift_tooltip","§c恶魔领主的力量\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.demon_lord_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.shadow_assassin_armor.shift_tooltip","§c影子议会刺客的盔甲\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.shadow_assassin_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.spring_armor.shift_tooltip","§c春天啊\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.spring_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.summer_armor.shift_tooltip","§c夏天啊\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.summer_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.autumn_armor.shift_tooltip","§c秋天啊\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.autumn_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("item.shannsmod.winter_armor.shift_tooltip","§c冬天啊\n§6小牛物语服务器 盗用追究");
        translationBuilder.add("item.shannsmod.winter_armor.tooltip","按下 \u00A76SHIFT \u00A7r以查看更多信息");
        translationBuilder.add("sounds.shannsmod.terror_xpp_attack","恐怖XPP攻击");
        translationBuilder.add("sounds.shannsmod.terror_xpp_hurt","恐怖XPP受伤");
        translationBuilder.add("sounds.shannsmod.terror_xpp_death","恐怖XPP死亡");


    }
}
