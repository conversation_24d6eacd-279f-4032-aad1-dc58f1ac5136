package com.besson.shannsmod.sound;

import com.besson.shannsmod.ShannsMod;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.sound.SoundEvent;
import net.minecraft.util.Identifier;


public class ModSoundEvents {
    public static final SoundEvent TERROR_XPP_ATTACK = register("terror_xpp_attack");
    public static final SoundEvent TERROR_XPP_HURT = register("terror_xpp_hurt");
    public static final SoundEvent TERROR_XPP_DEATH = register("terror_xpp_death");
    private static SoundEvent register(String name){
        Identifier id = Identifier.of(ShannsMod.MOD_ID,name);
        return Registry.register(Registries.SOUND_EVENT, id,SoundEvent.of(id));
    }
    public static void registerModSoundEvents(){

    }
}
