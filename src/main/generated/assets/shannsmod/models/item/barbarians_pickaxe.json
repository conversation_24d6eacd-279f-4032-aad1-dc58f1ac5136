{"format_version": "1.9.0", "credit": "Made with Blockbench", "texture_size": [64, 64], "textures": {"0": "shannsmod:item/barbarians_pickaxe", "particle": "shannsmod:item/barbarians_pickaxe"}, "elements": [{"from": [7, -1, 7], "to": [9, 7, 9], "rotation": {"angle": 0, "axis": "y", "origin": [7, 0, 7]}, "faces": {"north": {"uv": [3.5, 7.25, 4, 9.25], "texture": "#0"}, "east": {"uv": [3, 7.25, 3.5, 9.25], "texture": "#0"}, "south": {"uv": [4.5, 7.25, 5, 9.25], "texture": "#0"}, "west": {"uv": [4, 7.25, 4.5, 9.25], "texture": "#0"}, "up": {"uv": [4, 7.25, 3.5, 6.75], "texture": "#0"}, "down": {"uv": [4.5, 6.75, 4, 7.25], "texture": "#0"}}}, {"from": [6, -3, 6], "to": [10, -1, 10], "rotation": {"angle": -45, "axis": "y", "origin": [8, -3, 8]}, "faces": {"north": {"uv": [1, 6, 2, 6.5], "texture": "#0"}, "east": {"uv": [0, 6, 1, 6.5], "texture": "#0"}, "south": {"uv": [3, 6, 4, 6.5], "texture": "#0"}, "west": {"uv": [2, 6, 3, 6.5], "texture": "#0"}, "up": {"uv": [2, 6, 1, 5], "texture": "#0"}, "down": {"uv": [3, 5, 2, 6], "texture": "#0"}}}, {"from": [6.7, -5, 6.7], "to": [9.3, -2.4, 9.3], "rotation": {"angle": 0, "axis": "y", "origin": [8, -5.2, 8]}, "faces": {"north": {"uv": [8.75, 7.25, 9.5, 8], "texture": "#0"}, "east": {"uv": [8, 7.25, 8.75, 8], "texture": "#0"}, "south": {"uv": [10.25, 7.25, 11, 8], "texture": "#0"}, "west": {"uv": [9.5, 7.25, 10.25, 8], "texture": "#0"}, "up": {"uv": [9.5, 7.25, 8.75, 6.5], "texture": "#0"}, "down": {"uv": [10.25, 6.5, 9.5, 7.25], "texture": "#0"}}}, {"from": [7, -7.75, 6.75], "to": [9, -4.75, 8.75], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.5, -4.75, 6.75]}, "faces": {"north": {"uv": [5.5, 8.75, 6, 9.5], "texture": "#0"}, "east": {"uv": [5, 8.75, 5.5, 9.5], "texture": "#0"}, "south": {"uv": [6.5, 8.75, 7, 9.5], "texture": "#0"}, "west": {"uv": [6, 8.75, 6.5, 9.5], "texture": "#0"}, "up": {"uv": [6, 8.75, 5.5, 8.25], "texture": "#0"}, "down": {"uv": [6.5, 8.25, 6, 8.75], "texture": "#0"}}}, {"from": [7.3, -9.2803, 8.15999], "to": [8.7, -6.8803, 9.55999], "rotation": {"angle": -45, "axis": "x", "origin": [7.5, -7.3303, 8.35999]}, "faces": {"north": {"uv": [7.25, 8.5, 7.5, 9], "texture": "#0"}, "east": {"uv": [7, 8.5, 7.25, 9], "texture": "#0"}, "south": {"uv": [7.75, 8.5, 8, 9], "texture": "#0"}, "west": {"uv": [7.5, 8.5, 7.75, 9], "texture": "#0"}, "up": {"uv": [7.5, 8.5, 7.25, 8.25], "texture": "#0"}, "down": {"uv": [7.75, 8.25, 7.5, 8.5], "texture": "#0"}}}, {"from": [6.49, 6.99, 9.99], "to": [9.51, 9.01, 12.01], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 11]}, "faces": {"north": {"uv": [8, 2.25, 8.75, 2.75], "texture": "#0"}, "east": {"uv": [7.5, 2.25, 8, 2.75], "texture": "#0"}, "south": {"uv": [9.25, 2.25, 10, 2.75], "texture": "#0"}, "west": {"uv": [8.75, 2.25, 9.25, 2.75], "texture": "#0"}, "up": {"uv": [8.75, 2.25, 8, 1.75], "texture": "#0"}, "down": {"uv": [9.5, 1.75, 8.75, 2.25], "texture": "#0"}}}, {"from": [6.49, 6.99, 3.99], "to": [9.51, 9.01, 6.01], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 5]}, "faces": {"north": {"uv": [5.5, 2.25, 6.25, 2.75], "texture": "#0"}, "east": {"uv": [5, 2.25, 5.5, 2.75], "texture": "#0"}, "south": {"uv": [6.75, 2.25, 7.5, 2.75], "texture": "#0"}, "west": {"uv": [6.25, 2.25, 6.75, 2.75], "texture": "#0"}, "up": {"uv": [6.25, 2.25, 5.5, 1.75], "texture": "#0"}, "down": {"uv": [7, 1.75, 6.25, 2.25], "texture": "#0"}}}, {"from": [6.5, 7, 5], "to": [9.5, 9, 11], "rotation": {"angle": 0, "axis": "y", "origin": [8, 7, 8]}, "faces": {"north": {"uv": [1.5, 4.5, 2.25, 5], "texture": "#0"}, "east": {"uv": [0, 4.5, 1.5, 5], "texture": "#0"}, "south": {"uv": [3.75, 4.5, 4.5, 5], "texture": "#0"}, "west": {"uv": [2.25, 4.5, 3.75, 5], "texture": "#0"}, "up": {"uv": [2.25, 4.5, 1.5, 3], "texture": "#0"}, "down": {"uv": [3, 3, 2.25, 4.5], "texture": "#0"}}}, {"from": [8, 17, 10.5], "to": [8, 19.5, 15.5], "rotation": {"angle": -45, "axis": "x", "origin": [8, 17, 10.5]}, "faces": {"north": {"uv": [9.25, 9.25, 9.25, 10], "texture": "#0"}, "east": {"uv": [8, 9.25, 9.25, 10], "texture": "#0"}, "south": {"uv": [10.5, 9.25, 10.5, 10], "texture": "#0"}, "west": {"uv": [9.25, 9.25, 10.5, 10], "texture": "#0"}, "up": {"uv": [9.25, 9.25, 9.25, 8], "texture": "#0"}, "down": {"uv": [9.25, 8, 9.25, 9.25], "texture": "#0"}}}, {"from": [8, 15.25, 11], "to": [8, 17.75, 16], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 17, 10.5]}, "faces": {"north": {"uv": [1.25, 9.5, 1.25, 10.25], "texture": "#0"}, "east": {"uv": [0, 9.5, 1.25, 10.25], "texture": "#0"}, "south": {"uv": [2.5, 9.5, 2.5, 10.25], "texture": "#0"}, "west": {"uv": [1.25, 9.5, 2.5, 10.25], "texture": "#0"}, "up": {"uv": [1.25, 9.5, 1.25, 8.25], "texture": "#0"}, "down": {"uv": [1.25, 8.25, 1.25, 9.5], "texture": "#0"}}}, {"from": [6.5, 9, 6.5], "to": [9.5, 13, 9.5], "rotation": {"angle": 0, "axis": "y", "origin": [8, 9, 8]}, "faces": {"north": {"uv": [4.75, 5.75, 5.5, 6.75], "texture": "#0"}, "east": {"uv": [4, 5.75, 4.75, 6.75], "texture": "#0"}, "south": {"uv": [6.25, 5.75, 7, 6.75], "texture": "#0"}, "west": {"uv": [5.5, 5.75, 6.25, 6.75], "texture": "#0"}, "up": {"uv": [5.5, 5.75, 4.75, 5], "texture": "#0"}, "down": {"uv": [6.25, 5, 5.5, 5.75], "texture": "#0"}}}, {"from": [6, 12.5, 6], "to": [10, 18.5, 12], "rotation": {"angle": -45, "axis": "x", "origin": [8, 14.5, 8]}, "faces": {"north": {"uv": [1.5, 1.5, 2.5, 3], "texture": "#0"}, "east": {"uv": [0, 1.5, 1.5, 3], "texture": "#0"}, "south": {"uv": [4, 1.5, 5, 3], "texture": "#0"}, "west": {"uv": [2.5, 1.5, 4, 3], "texture": "#0"}, "up": {"uv": [2.5, 1.5, 1.5, 0], "texture": "#0"}, "down": {"uv": [3.5, 0, 2.5, 1.5], "texture": "#0"}}}, {"from": [6.01, 14.01, 12.01], "to": [9.99, 17.99, 13.99], "rotation": {"angle": 0, "axis": "y", "origin": [6.5, 16, 13]}, "faces": {"north": {"uv": [5.5, 7.25, 6.5, 8.25], "texture": "#0"}, "east": {"uv": [5, 7.25, 5.5, 8.25], "texture": "#0"}, "south": {"uv": [7, 7.25, 8, 8.25], "texture": "#0"}, "west": {"uv": [6.5, 7.25, 7, 8.25], "texture": "#0"}, "up": {"uv": [6.5, 7.25, 5.5, 6.75], "texture": "#0"}, "down": {"uv": [7.5, 6.75, 6.5, 7.25], "texture": "#0"}}}, {"from": [5.49, 13.49, 10.24], "to": [10.51, 18.51, 12.26], "rotation": {"angle": 0, "axis": "y", "origin": [7, 15.5, 11]}, "faces": {"north": {"uv": [5.5, 0.5, 6.75, 1.75], "texture": "#0"}, "east": {"uv": [5, 0.5, 5.5, 1.75], "texture": "#0"}, "south": {"uv": [7.25, 0.5, 8.5, 1.75], "texture": "#0"}, "west": {"uv": [6.75, 0.5, 7.25, 1.75], "texture": "#0"}, "up": {"uv": [6.75, 0.5, 5.5, 0], "texture": "#0"}, "down": {"uv": [8, 0, 6.75, 0.5], "texture": "#0"}}}, {"from": [6.49, 13.99, 2.99], "to": [9.51, 18.01, 6.01], "rotation": {"angle": 0, "axis": "y", "origin": [7, 15, 6]}, "faces": {"north": {"uv": [0.75, 7.25, 1.5, 8.25], "texture": "#0"}, "east": {"uv": [0, 7.25, 0.75, 8.25], "texture": "#0"}, "south": {"uv": [2.25, 7.25, 3, 8.25], "texture": "#0"}, "west": {"uv": [1.5, 7.25, 2.25, 8.25], "texture": "#0"}, "up": {"uv": [1.5, 7.25, 0.75, 6.5], "texture": "#0"}, "down": {"uv": [2.25, 6.5, 1.5, 7.25], "texture": "#0"}}}, {"from": [6.5, 14.5, -2], "to": [9.5, 17.5, 3], "rotation": {"angle": -22.5, "axis": "x", "origin": [7, 17.5, 3]}, "faces": {"north": {"uv": [5.75, 4.25, 6.5, 5], "texture": "#0"}, "east": {"uv": [4.5, 4.25, 5.75, 5], "texture": "#0"}, "south": {"uv": [7.75, 4.25, 8.5, 5], "texture": "#0"}, "west": {"uv": [6.5, 4.25, 7.75, 5], "texture": "#0"}, "up": {"uv": [6.5, 4.25, 5.75, 3], "texture": "#0"}, "down": {"uv": [7.25, 3, 6.5, 4.25], "texture": "#0"}}}, {"from": [7, 13.12464, -5.42806], "to": [9, 15.12464, -1.42806], "rotation": {"angle": -45, "axis": "x", "origin": [6.5, 15.12464, -1.42806]}, "faces": {"north": {"uv": [8, 6, 8.5, 6.5], "texture": "#0"}, "east": {"uv": [7, 6, 8, 6.5], "texture": "#0"}, "south": {"uv": [9.5, 6, 10, 6.5], "texture": "#0"}, "west": {"uv": [8.5, 6, 9.5, 6.5], "texture": "#0"}, "up": {"uv": [8.5, 6, 8, 5], "texture": "#0"}, "down": {"uv": [9, 5, 8.5, 6], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"translation": [0, 2.25, 0], "scale": [0.8, 0.8, 0.8]}, "thirdperson_lefthand": {"translation": [0, 2.25, 0], "scale": [0.8, 0.8, 0.8]}, "firstperson_righthand": {"rotation": [-8, 16, 0], "translation": [0, 2, 0], "scale": [0.65, 0.65, 0.65]}, "firstperson_lefthand": {"rotation": [-8, 16, 0], "translation": [0, 2, 0], "scale": [0.65, 0.65, 0.65]}, "ground": {"rotation": [90, 90, 0], "scale": [0.55, 0.55, 0.55]}, "gui": {"rotation": [-58, 56, 90], "translation": [0.75, 0, 0], "scale": [0.62, 0.62, 0.62]}, "head": {"rotation": [90, 22, 90], "translation": [2.25, 13.75, 0], "scale": [0.8, 0.8, 0.8]}, "fixed": {"rotation": [0, -90, 0], "translation": [0, 1.5, -0.5], "scale": [1.1, 1.1, 1.1]}}}