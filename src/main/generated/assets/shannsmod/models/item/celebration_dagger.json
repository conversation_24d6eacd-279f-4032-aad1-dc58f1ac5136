{"format_version": "1.9.0", "credit": "Made with Blockbench", "textures": {"0": "shannsmod:item/celebration_dagger", "particle": "shannsmod:item/celebration_dagger"}, "elements": [{"from": [7.5, -1, 7.75], "to": [8.5, 1, 8.25], "rotation": {"angle": 0, "axis": "y", "origin": [7.5, 0, 7.5]}, "faces": {"north": {"uv": [3, 4, 4, 6], "texture": "#0"}, "east": {"uv": [3, 8, 4, 10], "texture": "#0"}, "south": {"uv": [4, 8, 5, 10], "texture": "#0"}, "west": {"uv": [5, 8, 6, 10], "texture": "#0"}, "up": {"uv": [1, 12, 0, 11], "texture": "#0"}, "down": {"uv": [12, 0, 11, 1], "texture": "#0"}}}, {"from": [7.01, 1.01, 7.26], "to": [8.99, 1.99, 8.74], "rotation": {"angle": 0, "axis": "y", "origin": [7.5, 0, 7]}, "faces": {"north": {"uv": [8, 1, 10, 2], "texture": "#0"}, "east": {"uv": [6, 8, 8, 9], "texture": "#0"}, "south": {"uv": [8, 8, 10, 9], "texture": "#0"}, "west": {"uv": [0, 9, 2, 10], "texture": "#0"}, "up": {"uv": [2, 6, 0, 4], "texture": "#0"}, "down": {"uv": [6, 0, 4, 2], "texture": "#0"}}}, {"from": [7.01, 2.01, 7.76], "to": [8.99, 5.49, 8.24], "rotation": {"angle": 0, "axis": "y", "origin": [7.5, -1, 7]}, "faces": {"north": {"uv": [0, 0, 2, 4], "texture": "#0"}, "east": {"uv": [2, 4, 3, 8], "texture": "#0"}, "south": {"uv": [2, 0, 4, 4], "texture": "#0"}, "west": {"uv": [4, 2, 5, 6], "texture": "#0"}, "up": {"uv": [11, 3, 9, 2], "texture": "#0"}, "down": {"uv": [11, 3, 9, 4], "texture": "#0"}}}, {"from": [6.76, 5.51, 7.76], "to": [9.24, 6.49, 8.24], "rotation": {"angle": 0, "axis": "y", "origin": [7.5, 2.5, 7]}, "faces": {"north": {"uv": [7, 6, 10, 7], "texture": "#0"}, "east": {"uv": [1, 11, 2, 12], "texture": "#0"}, "south": {"uv": [7, 7, 10, 8], "texture": "#0"}, "west": {"uv": [2, 11, 3, 12], "texture": "#0"}, "up": {"uv": [3, 9, 0, 8], "texture": "#0"}, "down": {"uv": [11, 0, 8, 1], "texture": "#0"}}}, {"from": [7.25, 5.55, 7.75], "to": [9.025, 7.325, 8.25], "rotation": {"angle": 45, "axis": "z", "origin": [8, 6.3, 7]}, "faces": {"north": {"uv": [5, 2, 7, 4], "texture": "#0"}, "east": {"uv": [2, 9, 3, 11], "texture": "#0"}, "south": {"uv": [5, 4, 7, 6], "texture": "#0"}, "west": {"uv": [9, 4, 10, 6], "texture": "#0"}, "up": {"uv": [8, 10, 6, 9], "texture": "#0"}, "down": {"uv": [10, 9, 8, 10], "texture": "#0"}}}, {"from": [6.99, 1.24, 7.24], "to": [8.51, 2.76, 8.76], "rotation": {"angle": 45, "axis": "z", "origin": [8, 2.25, 6.5]}, "faces": {"north": {"uv": [0, 6, 2, 8], "texture": "#0"}, "east": {"uv": [6, 0, 8, 2], "texture": "#0"}, "south": {"uv": [3, 6, 5, 8], "texture": "#0"}, "west": {"uv": [5, 6, 7, 8], "texture": "#0"}, "up": {"uv": [9, 4, 7, 2], "texture": "#0"}, "down": {"uv": [9, 4, 7, 6], "texture": "#0"}}}, {"from": [5, 1.75, 7.5], "to": [7, 3, 8.5], "rotation": {"angle": 22.5, "axis": "z", "origin": [8, 4, 6.5]}, "faces": {"north": {"uv": [0, 10, 2, 11], "texture": "#0"}, "east": {"uv": [11, 2, 12, 3], "texture": "#0"}, "south": {"uv": [10, 1, 12, 2], "texture": "#0"}, "west": {"uv": [3, 11, 4, 12], "texture": "#0"}, "up": {"uv": [5, 11, 3, 10], "texture": "#0"}, "down": {"uv": [12, 4, 10, 5], "texture": "#0"}}}, {"from": [9, 1.75, 7.5], "to": [11, 3, 8.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [8, 4, 6.5]}, "faces": {"north": {"uv": [5, 10, 7, 11], "texture": "#0"}, "east": {"uv": [11, 3, 12, 4], "texture": "#0"}, "south": {"uv": [10, 5, 12, 6], "texture": "#0"}, "west": {"uv": [4, 11, 5, 12], "texture": "#0"}, "up": {"uv": [12, 7, 10, 6], "texture": "#0"}, "down": {"uv": [9, 10, 7, 11], "texture": "#0"}}}, {"from": [7.25, -2, 7.5], "to": [8.75, -1, 8.5], "rotation": {"angle": 0, "axis": "y", "origin": [7.5, -2, 6.5]}, "faces": {"north": {"uv": [10, 7, 12, 8], "texture": "#0"}, "east": {"uv": [5, 11, 6, 12], "texture": "#0"}, "south": {"uv": [10, 8, 12, 9], "texture": "#0"}, "west": {"uv": [6, 11, 7, 12], "texture": "#0"}, "up": {"uv": [11, 11, 9, 10], "texture": "#0"}, "down": {"uv": [12, 9, 10, 10], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, 90, 0], "translation": [0, 12.75, 0], "scale": [1.7, 1.7, 1.7]}, "thirdperson_lefthand": {"rotation": [0, 90, 0], "translation": [0, 12.75, 0], "scale": [1.7, 1.7, 1.7]}, "firstperson_righthand": {"rotation": [-9, 90, 0], "translation": [1.25, 11, -0.25], "scale": [1.5, 1.5, 1.5]}, "firstperson_lefthand": {"rotation": [-9, 90, 0], "translation": [1.25, 11, -0.25], "scale": [1.5, 1.5, 1.5]}, "ground": {"rotation": [-90, 0, 90], "translation": [-7.75, -2.75, 1.25], "scale": [1.5, 1.5, 1.5]}, "gui": {"rotation": [32, -34, 45], "translation": [-5.75, 7.25, 0], "scale": [1.65, 1.65, 1.65]}, "head": {"rotation": [0, 0, -117], "translation": [-4.25, 1.25, 0], "scale": [2, 2, 2]}, "fixed": {"translation": [0, 13.5, -0.5], "scale": [2.5, 2.5, 2.5]}}}