{"format_version": "1.9.0", "credit": "Made with Blockbench", "textures": {"0": "shannsmod:item/butcher_dagger", "particle": "shannsmod:item/butcher_dagger"}, "elements": [{"from": [6.7, -3.3, 7.2], "to": [9.3, -1.7, 8.8], "rotation": {"angle": 0, "axis": "y", "origin": [7, 0, 7]}, "faces": {"north": {"uv": [10, 9, 12, 10], "texture": "#0"}, "east": {"uv": [0, 12, 1, 13], "texture": "#0"}, "south": {"uv": [11, 6, 13, 7], "texture": "#0"}, "west": {"uv": [1, 12, 2, 13], "texture": "#0"}, "up": {"uv": [13, 8, 11, 7], "texture": "#0"}, "down": {"uv": [13, 8, 11, 9], "texture": "#0"}}}, {"from": [7, -4, 7.5], "to": [9, 1, 8.5], "rotation": {"angle": 0, "axis": "y", "origin": [7, 0, 7]}, "faces": {"north": {"uv": [8, 0, 10, 5], "texture": "#0"}, "east": {"uv": [10, 4, 11, 9], "texture": "#0"}, "south": {"uv": [8, 5, 10, 10], "texture": "#0"}, "west": {"uv": [8, 10, 9, 15], "texture": "#0"}, "up": {"uv": [13, 11, 11, 10], "texture": "#0"}, "down": {"uv": [13, 11, 11, 12], "texture": "#0"}}}, {"from": [6, 1, 7], "to": [10, 3, 9], "rotation": {"angle": 0, "axis": "y", "origin": [7, 5, 7]}, "faces": {"north": {"uv": [0, 10, 4, 12], "texture": "#0"}, "east": {"uv": [9, 10, 11, 12], "texture": "#0"}, "south": {"uv": [10, 0, 14, 2], "texture": "#0"}, "west": {"uv": [11, 4, 13, 6], "texture": "#0"}, "up": {"uv": [14, 4, 10, 2], "texture": "#0"}, "down": {"uv": [8, 10, 4, 12], "texture": "#0"}}}, {"from": [6.5, 3, 8], "to": [10.5, 13, 8], "rotation": {"angle": 0, "axis": "y", "origin": [7, 10, 7]}, "faces": {"north": {"uv": [0, 0, 4, 10], "texture": "#0"}, "east": {"uv": [0, 0, 0, 10], "texture": "#0"}, "south": {"uv": [4, 0, 8, 10], "texture": "#0"}, "west": {"uv": [0, 0, 0, 10], "texture": "#0"}, "up": {"uv": [4, 0, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 0], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, 90, 0], "translation": [0, 8.75, 0], "scale": [1.1, 1.1, 1.1]}, "thirdperson_lefthand": {"rotation": [0, -90, 0], "translation": [0, 8.75, 0], "scale": [1.1, 1.1, 1.1]}, "firstperson_righthand": {"rotation": [-9, 90, 0], "translation": [1.25, 7, -0.25], "scale": [0.9, 0.9, 0.9]}, "firstperson_lefthand": {"rotation": [-9, -90, 0], "translation": [1.25, 7, -0.25], "scale": [0.9, 0.9, 0.9]}, "ground": {"rotation": [-90, 0, 90], "translation": [-2.5, 0, 1.25], "scale": [0.9, 0.9, 0.9]}, "gui": {"rotation": [32, -34, 45], "translation": [-2.75, 2.75, 0], "scale": [1, 0.95, 1]}, "head": {"rotation": [0, 0, -117], "translation": [-9.5, 5, 0]}, "fixed": {"translation": [0, 5.5, -0.5], "scale": [1.4, 1.4, 1.4]}}}