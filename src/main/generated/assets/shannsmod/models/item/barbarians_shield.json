{"format_version": "1.9.0", "credit": "Made with Blockbench", "texture_size": [64, 64], "textures": {"0": "shannsmod:item/barbarians_shield", "particle": "shannsmod:item/barbarians_shield"}, "elements": [{"from": [3, 0, 6], "to": [13, 16, 8], "faces": {"north": {"uv": [0, 0, 2.5, 4], "texture": "#0"}, "south": {"uv": [2.5, 0, 5, 4], "texture": "#0"}}}, {"from": [0.7, 5.7, 4.6], "to": [3.3, 8.3, 9.2], "rotation": {"angle": -22.5, "axis": "x", "origin": [0, 6, 8]}, "faces": {"east": {"uv": [1, 8, 2, 8.5], "texture": "#0"}, "west": {"uv": [2, 8, 3, 8.5], "texture": "#0"}, "up": {"uv": [8.5, 8.75, 8, 7.75], "texture": "#0"}, "down": {"uv": [8.75, 0, 8.25, 1], "texture": "#0"}}}, {"from": [0.7, 5.7, 2], "to": [3.3, 10, 4.6], "rotation": {"angle": -22.5, "axis": "x", "origin": [0, 6, 8]}, "faces": {"north": {"uv": [7.75, 0.75, 8.25, 1.75], "texture": "#0"}, "east": {"uv": [7.75, 4, 8.25, 5], "texture": "#0"}, "south": {"uv": [7.75, 5.75, 8.25, 6.75], "texture": "#0"}, "west": {"uv": [6.5, 8.5, 7, 9.5], "texture": "#0"}, "up": {"uv": [7, 5.5, 6.5, 5], "texture": "#0"}, "down": {"uv": [8.75, 3.25, 8.25, 3.75], "texture": "#0"}}}, {"from": [1, 10, 2.3], "to": [3, 12.7, 4.3], "rotation": {"angle": -22.5, "axis": "x", "origin": [0, 6, 8]}, "faces": {"north": {"uv": [7.75, 0, 8.25, 0.75], "texture": "#0"}, "east": {"uv": [7.75, 3.25, 8.25, 4], "texture": "#0"}, "south": {"uv": [7.75, 5, 8.25, 5.75], "texture": "#0"}, "west": {"uv": [6.5, 7.75, 7, 8.5], "texture": "#0"}, "up": {"uv": [7, 5.5, 6.5, 5], "texture": "#0"}, "down": {"uv": [8.75, 3.25, 8.25, 3.75], "texture": "#0"}}}, {"from": [2, 10.0087, 0.16991], "to": [2, 12.7087, 2.16991], "rotation": {"angle": 0, "axis": "y", "origin": [2, 10.0087, 0.16991]}, "faces": {"east": {"uv": [4.5, 6.5, 5, 7.25], "texture": "#0"}, "west": {"uv": [4.5, 5.75, 5, 6.5], "texture": "#0"}}}, {"from": [14, 10.0087, 0.16991], "to": [14, 12.7087, 2.16991], "rotation": {"angle": 0, "axis": "y", "origin": [14, 10.0087, 0.16991]}, "faces": {"east": {"uv": [5, 5.75, 4.5, 6.5], "texture": "#0"}, "west": {"uv": [5, 6.5, 4.5, 7.25], "texture": "#0"}}}, {"from": [-3, 0, 6], "to": [3, 16, 8], "rotation": {"angle": 45, "axis": "y", "origin": [3, 0, 6]}, "faces": {"north": {"uv": [0, 4, 1.5, 8], "texture": "#0"}, "south": {"uv": [1.5, 4, 3, 8], "texture": "#0"}, "west": {"uv": [5.5, 5.75, 6, 9.75], "texture": "#0"}, "up": {"uv": [9, 2.25, 7.5, 1.75], "texture": "#0"}, "down": {"uv": [9, 2.25, 7.5, 2.75], "texture": "#0"}}}, {"from": [-4.29289, 9, 10.45711], "to": [0.70711, 12, 10.45711], "rotation": {"angle": -22.5, "axis": "z", "origin": [1.45711, 12, 10.45711]}, "faces": {"north": {"uv": [9.25, 0, 10, 1], "rotation": 90, "texture": "#0"}, "south": {"uv": [10, 0, 10.75, 1], "rotation": 270, "texture": "#0"}}}, {"from": [-4.29289, 4, 10.45711], "to": [0.70711, 7, 10.45711], "rotation": {"angle": 22.5, "axis": "z", "origin": [1.45711, 4, 10.45711]}, "faces": {"north": {"uv": [10, 0, 9.25, 1], "rotation": 90, "texture": "#0"}, "south": {"uv": [10.75, 0, 10, 1], "rotation": 270, "texture": "#0"}}}, {"from": [-4.79289, 6.5, 10.45711], "to": [0.20711, 9.5, 10.45711], "rotation": {"angle": 0, "axis": "y", "origin": [1.45711, 12, 10.45711]}, "faces": {"north": {"uv": [9.25, 0, 10, 1], "rotation": 90, "texture": "#0"}, "south": {"uv": [10, 0, 10.75, 1], "rotation": 270, "texture": "#0"}}}, {"from": [15.29289, 9, 10.45711], "to": [20.29289, 12, 10.45711], "rotation": {"angle": 22.5, "axis": "z", "origin": [14.54289, 12, 10.45711]}, "faces": {"north": {"uv": [9.25, 1, 10, 0], "rotation": 90, "texture": "#0"}, "south": {"uv": [10, 1, 10.75, 0], "rotation": 270, "texture": "#0"}}}, {"from": [15.79289, 6.5, 10.45711], "to": [20.79289, 9.5, 10.45711], "rotation": {"angle": 0, "axis": "y", "origin": [14.54289, 12, 10.45711]}, "faces": {"north": {"uv": [9.25, 1, 10, 0], "rotation": 90, "texture": "#0"}, "south": {"uv": [10, 1, 10.75, 0], "rotation": 270, "texture": "#0"}}}, {"from": [15.29289, 4, 10.45711], "to": [20.29289, 7, 10.45711], "rotation": {"angle": -22.5, "axis": "z", "origin": [14.54289, 4, 10.45711]}, "faces": {"north": {"uv": [10, 1, 9.25, 0], "rotation": 90, "texture": "#0"}, "south": {"uv": [10.75, 1, 10, 0], "rotation": 270, "texture": "#0"}}}, {"from": [13, 0, 6], "to": [19, 16, 8], "rotation": {"angle": -45, "axis": "y", "origin": [13, 0, 6]}, "faces": {"north": {"uv": [1.5, 4, 0, 8], "texture": "#0"}, "east": {"uv": [6, 5.75, 5.5, 9.75], "texture": "#0"}, "south": {"uv": [3, 4, 1.5, 8], "texture": "#0"}, "up": {"uv": [7.5, 2.25, 9, 1.75], "texture": "#0"}, "down": {"uv": [7.5, 2.25, 9, 2.75], "texture": "#0"}}}, {"from": [12.7, 5.7, 2], "to": [15.3, 10, 4.6], "rotation": {"angle": -22.5, "axis": "x", "origin": [16, 6, 8]}, "faces": {"north": {"uv": [8.25, 0.75, 7.75, 1.75], "texture": "#0"}, "east": {"uv": [7, 8.5, 6.5, 9.5], "texture": "#0"}, "south": {"uv": [8.25, 5.75, 7.75, 6.75], "texture": "#0"}, "west": {"uv": [8.25, 4, 7.75, 5], "texture": "#0"}, "up": {"uv": [6.5, 5.5, 7, 5], "texture": "#0"}, "down": {"uv": [8.25, 3.25, 8.75, 3.75], "texture": "#0"}}}, {"from": [13, 10, 2.3], "to": [15, 12.7, 4.3], "rotation": {"angle": -22.5, "axis": "x", "origin": [16, 6, 8]}, "faces": {"north": {"uv": [8.25, 0, 7.75, 0.75], "texture": "#0"}, "east": {"uv": [7, 7.75, 6.5, 8.5], "texture": "#0"}, "south": {"uv": [8.25, 5, 7.75, 5.75], "texture": "#0"}, "west": {"uv": [8.25, 3.25, 7.75, 4], "texture": "#0"}, "up": {"uv": [6.5, 5.5, 7, 5], "texture": "#0"}, "down": {"uv": [8.25, 3.25, 8.75, 3.75], "texture": "#0"}}}, {"from": [12.7, 5.7, 4.6], "to": [15.3, 8.3, 9.2], "rotation": {"angle": -22.5, "axis": "x", "origin": [16, 6, 8]}, "faces": {"east": {"uv": [3, 8, 2, 8.5], "texture": "#0"}, "west": {"uv": [2, 8, 1, 8.5], "texture": "#0"}, "up": {"uv": [8, 8.75, 8.5, 7.75], "texture": "#0"}, "down": {"uv": [8.25, 0, 8.75, 1], "texture": "#0"}}}, {"from": [5.925, 8.925, 5.98], "to": [13, 16, 8.02], "rotation": {"angle": -45, "axis": "z", "origin": [13, 16, 6]}, "faces": {"north": {"uv": [3, 4, 4.75, 5.75], "texture": "#0"}, "south": {"uv": [4.75, 4, 6.5, 5.75], "texture": "#0"}, "west": {"uv": [7.25, 5, 7.75, 6.75], "texture": "#0"}, "up": {"uv": [8.5, 3.25, 6.75, 2.75], "texture": "#0"}}}, {"from": [5.925, -7.075, 5.98], "to": [13, 0, 8.02], "rotation": {"angle": -45, "axis": "z", "origin": [13, 0, 6]}, "faces": {"north": {"uv": [5, 0, 6.75, 1.75], "texture": "#0"}, "east": {"uv": [6, 7.25, 6.5, 9], "texture": "#0"}, "south": {"uv": [5, 1.75, 6.75, 3.5], "texture": "#0"}, "down": {"uv": [8.25, 7.25, 6.5, 7.75], "texture": "#0"}}}, {"from": [6.5, 5, 8], "to": [9.5, 11, 13], "faces": {"east": {"uv": [6, 5.75, 7.25, 7.25], "texture": "#0"}, "south": {"uv": [6.75, 1.25, 7.5, 2.75], "texture": "#0"}, "west": {"uv": [6.5, 3.5, 7.75, 5], "texture": "#0"}, "up": {"uv": [3.75, 8.5, 3, 7.25], "texture": "#0"}, "down": {"uv": [4.5, 7.25, 3.75, 8.5], "texture": "#0"}}}, {"from": [9.5, 10, 12], "to": [6.5, 6, 8], "rotation": {"angle": 0, "axis": "y", "origin": [16, 16, 16]}, "faces": {"north": {"uv": [6.75, 2.75, 7.5, 1.25], "texture": "#0"}, "up": {"uv": [3.75, 7.5, 4.5, 8.5], "texture": "#0"}, "down": {"uv": [3, 8.5, 3.75, 7.5], "texture": "#0"}}}, {"from": [5, 7, 4], "to": [11, 13, 6], "rotation": {"angle": -45, "axis": "z", "origin": [8, 10, 5]}, "faces": {"north": {"uv": [3, 5.75, 4.5, 7.25], "texture": "#0"}, "east": {"uv": [7, 7.75, 7.5, 9.25], "texture": "#0"}, "west": {"uv": [7.5, 7.75, 8, 9.25], "texture": "#0"}, "up": {"uv": [6.5, 4, 5, 3.5], "texture": "#0"}, "down": {"uv": [8.75, 6.75, 7.25, 7.25], "texture": "#0"}}}, {"from": [6, 2.9, 4.02], "to": [10, 7.9, 6.02], "faces": {"north": {"uv": [6.75, 0, 7.75, 1.25], "texture": "#0"}, "east": {"uv": [0, 8, 0.5, 9.25], "texture": "#0"}, "west": {"uv": [0.5, 8, 1, 9.25], "texture": "#0"}, "down": {"uv": [9.25, 1, 8.25, 1.5], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, -90, 0], "translation": [3.5, -1, 2.5], "scale": [0.7, 0.7, 0.7]}, "thirdperson_lefthand": {"rotation": [0, -90, 0], "translation": [3.5, -1, 2.5], "scale": [0.7, 0.7, 0.7]}, "firstperson_righthand": {"rotation": [0, -9, 11], "translation": [0.25, 0.75, 1], "scale": [0.65, 0.65, 0.65]}, "firstperson_lefthand": {"rotation": [0, -9, 11], "translation": [0.25, 0.75, 1], "scale": [0.65, 0.65, 0.65]}, "ground": {"rotation": [90, 0, 0], "translation": [0, 0, 0.25], "scale": [0.55, 0.55, 0.55]}, "gui": {"rotation": [-155, 9.5, 145], "translation": [-0.5, 0.5, 0], "scale": [0.62, 0.62, 0.62]}, "head": {"rotation": [0, -180, 46], "translation": [2, -18.75, 7.75], "scale": [1.2, 1.2, 1.2]}, "fixed": {"translation": [0, 0.25, -3.5], "scale": [1.1, 1.1, 1.1]}}}