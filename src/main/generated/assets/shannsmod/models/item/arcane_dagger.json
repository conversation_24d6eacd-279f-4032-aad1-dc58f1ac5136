{"format_version": "1.21.6", "credit": "Made with Blockbench", "textures": {"0": "shannsmod:item/arcane_dagger", "particle": "shannsmod:item/arcane_dagger"}, "elements": [{"from": [6.75, 4.38084, -2.61916], "to": [9.75, 7.88084, 0.88084], "rotation": {"angle": -45, "axis": "x", "origin": [8, 15.25, 8.25]}, "faces": {"north": {"uv": [0, 7, 2, 9], "texture": "#0"}, "east": {"uv": [6, 6, 8, 8], "texture": "#0"}, "south": {"uv": [9, 5, 7, 3], "rotation": 270, "texture": "#0"}, "west": {"uv": [8, 6, 6, 8], "texture": "#0"}, "up": {"uv": [9, 5, 7, 3], "rotation": 90, "texture": "#0"}, "down": {"uv": [0, 7, 2, 9], "texture": "#0"}}}, {"from": [6.75, 9.10337, 3.10337], "to": [9.75, 15.10337, 9.10337], "rotation": {"angle": -45, "axis": "x", "origin": [8, 14.25, 8.25]}, "faces": {"north": {"uv": [5.5, 3, 7, 6], "texture": "#0"}, "east": {"uv": [0, 4, 3, 7], "texture": "#0"}, "south": {"uv": [6, 7.5, 3, 6], "rotation": 90, "texture": "#0"}, "west": {"uv": [3, 4, 0, 7], "texture": "#0"}, "up": {"uv": [3, 7.5, 6, 6], "rotation": 90, "texture": "#0"}, "down": {"uv": [5.5, 3, 7, 6], "texture": "#0"}}}, {"from": [9.75, 13.59332, 7.59332], "to": [6.75, 10.59332, 4.59332], "shade": false, "rotation": {"angle": -45, "axis": "x", "origin": [8, 14.25, 8.25]}, "faces": {"north": {"uv": [15, 1, 16, 2], "texture": "#0"}, "east": {"uv": [15, 1, 16, 2], "texture": "#0"}, "south": {"uv": [15, 1, 16, 2], "texture": "#0"}, "west": {"uv": [15, 1, 16, 2], "texture": "#0"}, "up": {"uv": [15, 1, 16, 2], "rotation": 90, "texture": "#0"}, "down": {"uv": [15, 1, 16, 2], "rotation": 270, "texture": "#0"}}}, {"from": [10.15, 13.99332, 7.99332], "to": [6.35, 10.19332, 4.19332], "shade": false, "rotation": {"angle": -45, "axis": "x", "origin": [8, 14.25, 8.25]}, "faces": {"north": {"uv": [15, 2, 16, 3], "texture": "#0"}, "east": {"uv": [15, 2, 16, 3], "texture": "#0"}, "south": {"uv": [15, 2, 16, 3], "texture": "#0"}, "west": {"uv": [15, 2, 16, 3], "texture": "#0"}, "up": {"uv": [15, 2, 16, 3], "rotation": 90, "texture": "#0"}, "down": {"uv": [15, 2, 16, 3], "rotation": 270, "texture": "#0"}}}, {"from": [7.15, 10.98327, 4.98327], "to": [9.35, 13.18327, 7.18327], "rotation": {"angle": -45, "axis": "x", "origin": [8, 14.25, 8.25]}, "faces": {"north": {"uv": [15, 0, 16, 1], "texture": "#0"}, "east": {"uv": [15, 0, 16, 1], "texture": "#0"}, "south": {"uv": [15, 0, 16, 1], "texture": "#0"}, "west": {"uv": [15, 0, 16, 1], "texture": "#0"}, "up": {"uv": [15, 0, 16, 1], "rotation": 90, "texture": "#0"}, "down": {"uv": [15, 0, 16, 1], "rotation": 270, "texture": "#0"}}}, {"from": [7.25, 13.35337, 1.75337], "to": [9.25, 16.35337, 5.75337], "rotation": {"angle": -45, "axis": "x", "origin": [8, 13.5, 8.9]}, "faces": {"north": {"uv": [7, 8, 8.5, 9.5], "texture": "#0"}, "east": {"uv": [3, 7.5, 5, 9], "texture": "#0"}, "south": {"uv": [8, 6.5, 9.5, 8], "texture": "#0"}, "west": {"uv": [5, 7.5, 3, 9], "texture": "#0"}, "up": {"uv": [7, 9.5, 5, 8], "rotation": 90, "texture": "#0"}, "down": {"uv": [10, 5, 8, 6.5], "rotation": 270, "texture": "#0"}}}, {"from": [7.25, 13.35337, 10.74663], "to": [9.25, 16.35337, 14.74663], "rotation": {"angle": 45, "axis": "x", "origin": [8, 13.5, 7.6]}, "faces": {"north": {"uv": [9.5, 6.5, 8, 8], "texture": "#0"}, "east": {"uv": [5, 7.5, 3, 9], "texture": "#0"}, "south": {"uv": [8.5, 8, 7, 9.5], "texture": "#0"}, "west": {"uv": [3, 7.5, 5, 9], "texture": "#0"}, "up": {"uv": [5, 9.5, 7, 8], "rotation": 90, "texture": "#0"}, "down": {"uv": [8, 5, 10, 6.5], "rotation": 270, "texture": "#0"}}}, {"from": [7.73, 11.24246, 6.99246], "to": [8.77, 16.94246, 12.69246], "rotation": {"angle": -45, "axis": "x", "origin": [8, 12.5, 8.25]}, "faces": {"north": {"uv": [8, 0, 8.5, 3], "texture": "#0"}, "east": {"uv": [5.5, 0, 8.5, 3], "texture": "#0"}, "south": {"uv": [5.5, 0, 6, 3], "texture": "#0"}, "west": {"uv": [8.5, 0, 5.5, 3], "texture": "#0"}, "up": {"uv": [8.5, 0, 5.5, 0.5], "rotation": 90, "texture": "#0"}, "down": {"uv": [8.5, 2.5, 5.5, 3], "rotation": 270, "texture": "#0"}}}, {"from": [7.25, 2, 7.25], "to": [9.25, 8, 9.25], "rotation": {"angle": 0, "axis": "y", "origin": [8, 14.25, 8.25]}, "faces": {"north": {"uv": [2, 7, 3, 10], "texture": "#0"}, "east": {"uv": [2, 7, 3, 10], "texture": "#0"}, "south": {"uv": [2, 7, 3, 10], "texture": "#0"}, "west": {"uv": [2, 7, 3, 10], "texture": "#0"}}}, {"from": [7.75, 12.75, 6.25], "to": [8.75, 24.75, 10.25], "rotation": {"angle": 0, "axis": "y", "origin": [8, 12.5, 8.25]}, "faces": {"north": {"uv": [5, 0, 5.5, 6], "texture": "#0"}, "east": {"uv": [3.5, 0, 5.5, 6], "texture": "#0"}, "south": {"uv": [3.5, 0, 4, 6], "texture": "#0"}, "west": {"uv": [5.5, 0, 3.5, 6], "texture": "#0"}, "up": {"uv": [5.5, 0, 3.5, 0.5], "rotation": 90, "texture": "#0"}}}, {"from": [8.2, 20.75, 5.25], "to": [8.25, 28.75, 11.25], "rotation": {"angle": 0, "axis": "y", "origin": [8, 11.5, 8.25]}, "faces": {"east": {"uv": [0, 0, 3, 4], "texture": "#0"}, "west": {"uv": [3, 0, 0, 4], "texture": "#0"}}}, {"from": [9.81, 14.11343, 8.11343], "to": [6.77, 10.11343, 4.11343], "rotation": {"angle": -45, "axis": "x", "origin": [8, 14.25, 8.25]}, "faces": {"north": {"uv": [5.5, 7.5, 3.5, 6], "rotation": 90, "texture": "#0"}, "south": {"uv": [5.5, 3.5, 7, 5.5], "texture": "#0"}, "up": {"uv": [5.5, 5.5, 7, 3.5], "rotation": 180, "texture": "#0"}, "down": {"uv": [5.5, 7.5, 3.5, 6], "rotation": 270, "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, -180, 0], "translation": [0, 0.25, 0], "scale": [0.65, 0.65, 0.65]}, "thirdperson_lefthand": {"rotation": [0, -180, 0], "translation": [0, 0.25, 0], "scale": [0.65, 0.65, 0.65]}, "firstperson_righthand": {"rotation": [-2, -180, -2], "translation": [1.25, 0.75, 0], "scale": [0.55, 0.55, 0.55]}, "firstperson_lefthand": {"rotation": [-2, -180, -2], "translation": [1.25, 0.75, 0], "scale": [0.55, 0.55, 0.55]}, "ground": {"rotation": [94, 85, 0], "translation": [0, 0, -3.5], "scale": [0.45, 0.45, 0.45]}, "gui": {"rotation": [135, -58, 100], "translation": [1.25, -2.5, 0], "scale": [0.65, 0.65, 0.65]}, "head": {"rotation": [90, 22, 90], "translation": [7.75, 12, 0.25], "scale": [0.75, 0.75, 0.75]}, "fixed": {"rotation": [0, 90, 0], "translation": [0, -6.5, -0.25], "scale": [0.95, 0.95, 0.95]}}}