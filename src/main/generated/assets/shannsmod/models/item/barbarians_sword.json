{"format_version": "1.9.0", "credit": "Made with Blockbench", "texture_size": [32, 32], "textures": {"0": "shannsmod:item/barbarians_sword", "particle": "shannsmod:item/barbarians_sword"}, "elements": [{"from": [6, -16, 6], "to": [10, -12, 10], "rotation": {"angle": 45, "axis": "z", "origin": [8, -14, 0]}, "faces": {"north": {"uv": [8, 5, 10, 7], "texture": "#0"}, "east": {"uv": [9.5, 9, 11.5, 7], "rotation": 90, "texture": "#0"}, "south": {"uv": [8, 5, 10, 7], "rotation": 90, "texture": "#0"}, "west": {"uv": [7, 9.5, 9, 11.5], "texture": "#0"}, "up": {"uv": [11.5, 9, 9.5, 7], "texture": "#0"}, "down": {"uv": [7, 9.5, 9, 11.5], "rotation": 90, "texture": "#0"}}}, {"from": [6, -4, 6], "to": [11, 1, 10], "rotation": {"angle": 45, "axis": "z", "origin": [8, -2, 0]}, "faces": {"north": {"uv": [4, 3.5, 6.5, 6], "texture": "#0"}, "east": {"uv": [7, 9.5, 9.5, 7.5], "rotation": 90, "texture": "#0"}, "south": {"uv": [4, 3.5, 6.5, 6], "rotation": 90, "texture": "#0"}, "west": {"uv": [5, 7.5, 7, 10], "texture": "#0"}, "up": {"uv": [9.5, 9.5, 7, 7.5], "texture": "#0"}, "down": {"uv": [5, 7.5, 7, 10], "rotation": 90, "texture": "#0"}}}, {"from": [6, -11, 6], "to": [10, -9, 10], "faces": {"north": {"uv": [11, 2, 13, 3], "texture": "#0"}, "east": {"uv": [11, 2, 13, 3], "texture": "#0"}, "south": {"uv": [11, 2, 13, 3], "texture": "#0"}, "west": {"uv": [11, 2, 13, 3], "texture": "#0"}, "up": {"uv": [11, 11.5, 9, 9.5], "texture": "#0"}, "down": {"uv": [7, 10, 5, 12], "texture": "#0"}}}, {"from": [7, -13, 7], "to": [9, -3, 9], "faces": {"north": {"uv": [4, 7.5, 5, 12.5], "texture": "#0"}, "east": {"uv": [4, 7.5, 5, 12.5], "texture": "#0"}, "south": {"uv": [4, 7.5, 5, 12.5], "texture": "#0"}, "west": {"uv": [4, 7.5, 5, 12.5], "texture": "#0"}}}, {"from": [4, -3, 7], "to": [12, 0, 9], "rotation": {"angle": 0, "axis": "y", "origin": [0, -2, 7]}, "faces": {"north": {"uv": [4, 6, 8, 7.5], "texture": "#0"}, "south": {"uv": [6.5, 3.5, 10.5, 5], "texture": "#0"}, "up": {"uv": [14, 6, 10, 5], "texture": "#0"}, "down": {"uv": [14, 6, 10, 7], "texture": "#0"}}}, {"from": [5, -7, 8], "to": [7, -3, 8], "rotation": {"angle": -22.5, "axis": "z", "origin": [7, -3, 8]}, "faces": {"north": {"uv": [11.5, 7, 13, 9], "texture": "#0"}, "south": {"uv": [13, 7, 14.5, 9], "texture": "#0"}}}, {"from": [3, -7, 8], "to": [5, -3, 8], "rotation": {"angle": -45, "axis": "z", "origin": [5, -3, 8]}, "faces": {"north": {"uv": [11.5, 7, 13, 9], "texture": "#0"}, "south": {"uv": [13, 7, 14.5, 9], "texture": "#0"}}}, {"from": [11, -7, 8], "to": [13, -3, 8], "rotation": {"angle": 45, "axis": "z", "origin": [11, -3, 8]}, "faces": {"north": {"uv": [13, 7, 11.5, 9], "texture": "#0"}, "south": {"uv": [14.5, 7, 13, 9], "texture": "#0"}}}, {"from": [9, -7, 8], "to": [11, -3, 8], "rotation": {"angle": 22.5, "axis": "z", "origin": [9, -3, 8]}, "faces": {"north": {"uv": [13, 7, 11.5, 9], "texture": "#0"}, "south": {"uv": [14.5, 7, 13, 9], "texture": "#0"}}}, {"from": [12, -3, 6.98], "to": [17, 0, 9.02], "rotation": {"angle": 45, "axis": "z", "origin": [12, -3, 7]}, "faces": {"north": {"uv": [2.5, 10.5, 0.1, 12], "texture": "#0"}, "east": {"uv": [3.5, 10.5, 2.5, 12], "texture": "#0"}, "south": {"uv": [12.9, 3.5, 10.6, 5], "texture": "#0"}, "up": {"uv": [11.1, 1, 13.5, 0], "texture": "#0"}, "down": {"uv": [11.1, 1, 13.5, 2], "texture": "#0"}}}, {"from": [-1, -3, 6.98], "to": [4, 0, 9.02], "rotation": {"angle": -45, "axis": "z", "origin": [4, -3, 7]}, "faces": {"north": {"uv": [0.1, 10.5, 2.5, 12], "texture": "#0"}, "south": {"uv": [10.5, 3.5, 12.9, 5], "texture": "#0"}, "west": {"uv": [2.5, 10.5, 3.5, 12], "texture": "#0"}, "up": {"uv": [13.5, 1, 11.1, 0], "texture": "#0"}, "down": {"uv": [13.5, 1, 11.1, 2], "texture": "#0"}}}, {"from": [4, 0, 7.5], "to": [12, 21, 8.5], "faces": {"north": {"uv": [0, 0, 4, 10.5], "texture": "#0"}, "east": {"uv": [0.5, 0, 0, 10.5], "texture": "#0"}, "south": {"uv": [4, 0, 0, 10.5], "texture": "#0"}, "west": {"uv": [4, 0, 3.5, 10.5], "texture": "#0"}, "up": {"uv": [4, 0, 0, 0.5], "texture": "#0"}}}, {"from": [1, 19, 8], "to": [15, 26, 8.05], "faces": {"north": {"uv": [4, 0, 11, 3.5], "texture": "#0"}, "south": {"uv": [11, 0, 4, 3.5], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, 90, 0], "translation": [0, 9.25, 0], "scale": [0.7, 0.7, 0.7]}, "thirdperson_lefthand": {"rotation": [0, 90, 0], "translation": [0, 9.25, 0], "scale": [0.7, 0.7, 0.7]}, "firstperson_righthand": {"rotation": [-9, 90, 0], "translation": [0.5, 6.5, 0], "scale": [0.6, 0.6, 0.6]}, "firstperson_lefthand": {"rotation": [-9, 90, 0], "translation": [0.5, 6.5, 0], "scale": [0.6, 0.6, 0.6]}, "ground": {"rotation": [90, -180, 0], "translation": [0, 0, 1], "scale": [0.5, 0.5, 0.5]}, "gui": {"rotation": [32, -34, 45], "translation": [-2, 1.5, 0], "scale": [0.48, 0.48, 0.48]}, "head": {"rotation": [0, 0, 117], "translation": [2.75, 9.5, 0]}, "fixed": {"rotation": [0, -180, 0], "translation": [0, 4, -0.5]}}}