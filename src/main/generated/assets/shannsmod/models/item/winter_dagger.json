{"format_version": "1.9.0", "credit": "Made with Blockbench", "texture_size": [32, 32], "textures": {"0": "shannsmod:item/winter_dagger", "particle": "shannsmod:item/winter_dagger"}, "elements": [{"from": [5.99, -0.01, 3.49], "to": [10.01, 2.01, 10.51], "rotation": {"angle": 0, "axis": "y", "origin": [0, 0, -0.5]}, "faces": {"north": {"uv": [5, 0, 7, 1], "texture": "#0"}, "east": {"uv": [0, 3.5, 3.5, 4.5], "texture": "#0"}, "south": {"uv": [5, 1, 7, 2], "texture": "#0"}, "west": {"uv": [3.5, 3.5, 7, 4.5], "texture": "#0"}, "up": {"uv": [2, 3.5, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 2, 3.5], "texture": "#0"}}}, {"from": [5.74, 1.99, -3.01], "to": [6.76, 3.01, 2.01], "rotation": {"angle": -22.5, "axis": "x", "origin": [6.75, 2, 4]}, "faces": {"north": {"uv": [3, 10, 3.5, 10.5], "texture": "#0"}, "east": {"uv": [7, 1, 9.5, 1.5], "texture": "#0"}, "south": {"uv": [10, 3, 10.5, 3.5], "texture": "#0"}, "west": {"uv": [7, 1.5, 9.5, 2], "texture": "#0"}, "up": {"uv": [3.5, 10, 3, 7.5], "texture": "#0"}, "down": {"uv": [4, 7.5, 3.5, 10], "texture": "#0"}}}, {"from": [7.49, 1.99, -3.51], "to": [8.51, 3.01, 1.51], "rotation": {"angle": -22.5, "axis": "x", "origin": [7, 2, 3.5]}, "faces": {"north": {"uv": [4, 10, 4.5, 10.5], "texture": "#0"}, "east": {"uv": [7.5, 4.5, 10, 5], "texture": "#0"}, "south": {"uv": [10, 4, 10.5, 4.5], "texture": "#0"}, "west": {"uv": [5, 7.5, 7.5, 8], "texture": "#0"}, "up": {"uv": [8, 9, 7.5, 6.5], "texture": "#0"}, "down": {"uv": [0.5, 8, 0, 10.5], "texture": "#0"}}}, {"from": [9.24, 1.99, -3.01], "to": [10.26, 3.01, 2.01], "rotation": {"angle": -22.5, "axis": "x", "origin": [9.25, 2, 4]}, "faces": {"north": {"uv": [3.5, 10, 4, 10.5], "texture": "#0"}, "east": {"uv": [7, 3.5, 9.5, 4], "texture": "#0"}, "south": {"uv": [10, 3.5, 10.5, 4], "texture": "#0"}, "west": {"uv": [7, 4, 9.5, 4.5], "texture": "#0"}, "up": {"uv": [4.5, 10, 4, 7.5], "texture": "#0"}, "down": {"uv": [5, 7.5, 4.5, 10], "texture": "#0"}}}, {"from": [5.74, 0.99, -3.01], "to": [6.76, 2.01, -1.99], "rotation": {"angle": -22.5, "axis": "x", "origin": [6.75, 2, 4]}, "faces": {"north": {"uv": [7.5, 10, 8, 10.5], "texture": "#0"}, "east": {"uv": [10, 7.5, 10.5, 8], "texture": "#0"}, "south": {"uv": [8, 10, 8.5, 10.5], "texture": "#0"}, "west": {"uv": [10, 8, 10.5, 8.5], "texture": "#0"}, "up": {"uv": [9, 10.5, 8.5, 10], "texture": "#0"}, "down": {"uv": [10.5, 8.5, 10, 9], "texture": "#0"}}}, {"from": [7.49, 0.99, -3.51], "to": [8.51, 2.01, -2.49], "rotation": {"angle": -22.5, "axis": "x", "origin": [7, 2, 3.5]}, "faces": {"north": {"uv": [6, 10, 6.5, 10.5], "texture": "#0"}, "east": {"uv": [10, 6, 10.5, 6.5], "texture": "#0"}, "south": {"uv": [6.5, 10, 7, 10.5], "texture": "#0"}, "west": {"uv": [10, 6.5, 10.5, 7], "texture": "#0"}, "up": {"uv": [7.5, 10.5, 7, 10], "texture": "#0"}, "down": {"uv": [10.5, 7, 10, 7.5], "texture": "#0"}}}, {"from": [9.24, 0.99, -3.01], "to": [10.26, 2.01, -1.99], "rotation": {"angle": -22.5, "axis": "x", "origin": [9.25, 2, 4]}, "faces": {"north": {"uv": [4.5, 10, 5, 10.5], "texture": "#0"}, "east": {"uv": [10, 4.5, 10.5, 5], "texture": "#0"}, "south": {"uv": [5, 10, 5.5, 10.5], "texture": "#0"}, "west": {"uv": [10, 5, 10.5, 5.5], "texture": "#0"}, "up": {"uv": [6, 10.5, 5.5, 10], "texture": "#0"}, "down": {"uv": [10.5, 5.5, 10, 6], "texture": "#0"}}}, {"from": [7.49, 0.99, 1.49], "to": [8.51, 3.01, 4.51], "rotation": {"angle": -22.5, "axis": "x", "origin": [7, 2, 3.5]}, "faces": {"north": {"uv": [9.5, 1.5, 10, 2.5], "texture": "#0"}, "east": {"uv": [6, 6.5, 7.5, 7.5], "texture": "#0"}, "south": {"uv": [2, 9.5, 2.5, 10.5], "texture": "#0"}, "west": {"uv": [7, 0, 8.5, 1], "texture": "#0"}, "up": {"uv": [7.5, 9.5, 7, 8], "texture": "#0"}, "down": {"uv": [8.5, 7.5, 8, 9], "texture": "#0"}}}, {"from": [5.74, 0.99, 1.99], "to": [6.76, 3.01, 5.01], "rotation": {"angle": -22.5, "axis": "x", "origin": [6.75, 2, 4]}, "faces": {"north": {"uv": [9.5, 0.5, 10, 1.5], "texture": "#0"}, "east": {"uv": [4.5, 6.5, 6, 7.5], "texture": "#0"}, "south": {"uv": [1.5, 9.5, 2, 10.5], "texture": "#0"}, "west": {"uv": [6.5, 5.5, 8, 6.5], "texture": "#0"}, "up": {"uv": [8.5, 7.5, 8, 6], "texture": "#0"}, "down": {"uv": [7, 8, 6.5, 9.5], "texture": "#0"}}}, {"from": [9.24, 0.99, 1.99], "to": [10.26, 3.01, 5.01], "rotation": {"angle": -22.5, "axis": "x", "origin": [9.25, 2, 4]}, "faces": {"north": {"uv": [1.5, 8.5, 2, 9.5], "texture": "#0"}, "east": {"uv": [6, 4.5, 7.5, 5.5], "texture": "#0"}, "south": {"uv": [6, 8.5, 6.5, 9.5], "texture": "#0"}, "west": {"uv": [3, 6.5, 4.5, 7.5], "texture": "#0"}, "up": {"uv": [2.5, 9.5, 2, 8], "texture": "#0"}, "down": {"uv": [3, 8, 2.5, 9.5], "texture": "#0"}}}, {"from": [6.49, 1.99, 5.99], "to": [9.51, 3.01, 9.01], "rotation": {"angle": -45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [7.5, 5, 9, 5.5], "texture": "#0"}, "east": {"uv": [0.5, 8, 2, 8.5], "texture": "#0"}, "south": {"uv": [5, 8, 6.5, 8.5], "texture": "#0"}, "west": {"uv": [8, 5.5, 9.5, 6], "texture": "#0"}, "up": {"uv": [4.5, 6, 3, 4.5], "texture": "#0"}, "down": {"uv": [6, 4.5, 4.5, 6], "texture": "#0"}}}, {"from": [9.49, 1.99, 6.99], "to": [11.51, 3.01, 8.01], "rotation": {"angle": -45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [8.5, 2.5, 9.5, 3], "texture": "#0"}, "east": {"uv": [6, 5.5, 6.5, 6], "texture": "#0"}, "south": {"uv": [8.5, 3, 9.5, 3.5], "texture": "#0"}, "west": {"uv": [2.5, 9.5, 3, 10], "texture": "#0"}, "up": {"uv": [6, 9, 5, 8.5], "texture": "#0"}, "down": {"uv": [9.5, 6, 8.5, 6.5], "texture": "#0"}}}, {"from": [10.49, 1.99, 7.99], "to": [11.51, 3.01, 9.01], "rotation": {"angle": -45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [9.5, 2.5, 10, 3], "texture": "#0"}, "east": {"uv": [9.5, 3, 10, 3.5], "texture": "#0"}, "south": {"uv": [9.5, 3.5, 10, 4], "texture": "#0"}, "west": {"uv": [9.5, 4, 10, 4.5], "texture": "#0"}, "up": {"uv": [5.5, 10, 5, 9.5], "texture": "#0"}, "down": {"uv": [6, 9.5, 5.5, 10], "texture": "#0"}}}, {"from": [4.49, 1.99, 7.99], "to": [5.51, 3.01, 9.01], "rotation": {"angle": 45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [9.5, 6, 10, 6.5], "texture": "#0"}, "east": {"uv": [6.5, 9.5, 7, 10], "texture": "#0"}, "south": {"uv": [9.5, 6.5, 10, 7], "texture": "#0"}, "west": {"uv": [7, 9.5, 7.5, 10], "texture": "#0"}, "up": {"uv": [10, 7.5, 9.5, 7], "texture": "#0"}, "down": {"uv": [8, 9.5, 7.5, 10], "texture": "#0"}}}, {"from": [4.49, 1.99, 6.99], "to": [6.51, 3.01, 8.01], "rotation": {"angle": 45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [8.5, 6.5, 9.5, 7], "texture": "#0"}, "east": {"uv": [9.5, 5.5, 10, 6], "texture": "#0"}, "south": {"uv": [8.5, 7, 9.5, 7.5], "texture": "#0"}, "west": {"uv": [6, 9.5, 6.5, 10], "texture": "#0"}, "up": {"uv": [9.5, 8, 8.5, 7.5], "texture": "#0"}, "down": {"uv": [9.5, 8, 8.5, 8.5], "texture": "#0"}}}, {"from": [4.49, 1.99, 5.99], "to": [5.51, 3.01, 7.01], "rotation": {"angle": -45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [9.5, 8, 10, 8.5], "texture": "#0"}, "east": {"uv": [8.5, 9.5, 9, 10], "texture": "#0"}, "south": {"uv": [9.5, 8.5, 10, 9], "texture": "#0"}, "west": {"uv": [9, 9.5, 9.5, 10], "texture": "#0"}, "up": {"uv": [10, 9.5, 9.5, 9], "texture": "#0"}, "down": {"uv": [10, 9.5, 9.5, 10], "texture": "#0"}}}, {"from": [4.49, 1.99, 6.99], "to": [6.51, 3.01, 8.01], "rotation": {"angle": -45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [8.5, 8.5, 9.5, 9], "texture": "#0"}, "east": {"uv": [9.5, 7.5, 10, 8], "texture": "#0"}, "south": {"uv": [0.5, 9, 1.5, 9.5], "texture": "#0"}, "west": {"uv": [8, 9.5, 8.5, 10], "texture": "#0"}, "up": {"uv": [6, 9.5, 5, 9], "texture": "#0"}, "down": {"uv": [10, 5, 9, 5.5], "texture": "#0"}}}, {"from": [10.49, 1.99, 5.99], "to": [11.51, 3.01, 7.01], "rotation": {"angle": 45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [1, 10, 1.5, 10.5], "texture": "#0"}, "east": {"uv": [10, 1, 10.5, 1.5], "texture": "#0"}, "south": {"uv": [10, 1.5, 10.5, 2], "texture": "#0"}, "west": {"uv": [10, 2, 10.5, 2.5], "texture": "#0"}, "up": {"uv": [3, 10.5, 2.5, 10], "texture": "#0"}, "down": {"uv": [10.5, 2.5, 10, 3], "texture": "#0"}}}, {"from": [9.49, 1.99, 6.99], "to": [11.51, 3.01, 8.01], "rotation": {"angle": 45, "axis": "y", "origin": [8, 3, 7.5]}, "faces": {"north": {"uv": [7.5, 9, 8.5, 9.5], "texture": "#0"}, "east": {"uv": [0.5, 10, 1, 10.5], "texture": "#0"}, "south": {"uv": [8.5, 9, 9.5, 9.5], "texture": "#0"}, "west": {"uv": [10, 0.5, 10.5, 1], "texture": "#0"}, "up": {"uv": [10.5, 0.5, 9.5, 0], "texture": "#0"}, "down": {"uv": [1.5, 9.5, 0.5, 10], "texture": "#0"}}}, {"from": [4, 0.5, 3.5], "to": [6, 1.5, 10.5], "rotation": {"angle": 22.5, "axis": "z", "origin": [6, 1.5, 6]}, "faces": {"north": {"uv": [8.5, 0, 9.5, 0.5], "texture": "#0"}, "east": {"uv": [5, 2, 8.5, 2.5], "texture": "#0"}, "south": {"uv": [0.5, 8.5, 1.5, 9], "texture": "#0"}, "west": {"uv": [5, 2.5, 8.5, 3], "texture": "#0"}, "up": {"uv": [5, 3.5, 4, 0], "texture": "#0"}, "down": {"uv": [1, 4.5, 0, 8], "texture": "#0"}}}, {"from": [10, 0.5, 3.5], "to": [12, 1.5, 10.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [10, 1.5, 6]}, "faces": {"north": {"uv": [8.5, 0.5, 9.5, 1], "texture": "#0"}, "east": {"uv": [5, 3, 8.5, 3.5], "texture": "#0"}, "south": {"uv": [8.5, 2, 9.5, 2.5], "texture": "#0"}, "west": {"uv": [3, 6, 6.5, 6.5], "texture": "#0"}, "up": {"uv": [2, 8, 1, 4.5], "texture": "#0"}, "down": {"uv": [3, 4.5, 2, 8], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, 0, -90], "translation": [8.75, -2, 4.25], "scale": [1.1, 1.1, 1.1]}, "thirdperson_lefthand": {"rotation": [0, 0, -90], "translation": [8.75, -2, 4.25], "scale": [1.1, 1.1, 1.1]}, "firstperson_righthand": {"rotation": [34, -8, 7], "translation": [1.25, 4.75, 5], "scale": [1.1, 1.1, 1.1]}, "firstperson_lefthand": {"rotation": [34, -8, 7], "translation": [1.25, 4.75, 5], "scale": [1.1, 1.1, 1.1]}, "ground": {"translation": [0, 4.25, 2.5], "scale": [0.75, 0.75, 0.75]}, "gui": {"rotation": [88, 37, 40], "translation": [-2, -5, 0], "scale": [0.9, 0.9, 0.9]}, "head": {"rotation": [-90, 60, 90], "translation": [1.5, 22.75, 0], "scale": [1.3, 1.3, 1.3]}, "fixed": {"rotation": [-99, -180, 0], "translation": [0, -6, -8.25], "scale": [1.2, 1.2, 1.2]}}}