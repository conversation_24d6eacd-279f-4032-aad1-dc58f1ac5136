{"format_version": "1.9.0", "credit": "Made with Blockbench", "texture_size": [32, 32], "textures": {"0": "shannsmod:item/barbarians_dagger", "particle": "shannsmod:item/barbarians_dagger"}, "elements": [{"from": [6, 1, 6], "to": [10, 5, 10], "rotation": {"angle": 45, "axis": "z", "origin": [8, 3, 0]}, "faces": {"north": {"uv": [4, 3.5, 6.5, 6], "texture": "#0"}, "east": {"uv": [7, 9.5, 9.5, 7.5], "rotation": 90, "texture": "#0"}, "south": {"uv": [4, 3.5, 6.5, 6], "rotation": 90, "texture": "#0"}, "west": {"uv": [5, 7.5, 7, 10], "texture": "#0"}, "up": {"uv": [9.5, 9.5, 7, 7.5], "texture": "#0"}, "down": {"uv": [5, 7.5, 7, 10], "rotation": 90, "texture": "#0"}}}, {"from": [7.24, -1.01, 6.99], "to": [9.26, 2.01, 9.01], "rotation": {"angle": -22.5, "axis": "z", "origin": [9.25, 1, 8]}, "faces": {"north": {"uv": [4, 7.5, 5, 9], "texture": "#0"}, "east": {"uv": [4, 7.5, 5, 9], "texture": "#0"}, "south": {"uv": [4, 7.5, 5, 9], "texture": "#0"}, "west": {"uv": [4, 7.5, 5, 9], "texture": "#0"}}}, {"from": [5.90771, -3.14324, 6.75], "to": [8.90771, -1.14324, 9.25], "rotation": {"angle": 22.5, "axis": "z", "origin": [9.25, 1, 8]}, "faces": {"north": {"uv": [11, 2, 13, 3], "texture": "#0"}, "east": {"uv": [11, 2, 13, 3], "texture": "#0"}, "south": {"uv": [11, 2, 13, 3], "texture": "#0"}, "west": {"uv": [11, 2, 13, 3], "texture": "#0"}, "up": {"uv": [11, 11.5, 9, 9.5], "texture": "#0"}, "down": {"uv": [7, 10, 5, 12], "texture": "#0"}}}, {"from": [9.83161, -4.18701, 6.5], "to": [11.83161, -2.18701, 9.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [9.25, 1, 8]}, "faces": {"north": {"uv": [8, 5, 10, 7], "rotation": 90, "texture": "#0"}, "east": {"uv": [7.5, 9.5, 8.5, 11.5], "texture": "#0"}, "south": {"uv": [8, 5, 10, 7], "texture": "#0"}, "west": {"uv": [11.5, 8.5, 9.5, 7.5], "rotation": 270, "texture": "#0"}, "up": {"uv": [9.5, 8.5, 11.5, 7.5], "texture": "#0"}, "down": {"uv": [7.5, 9.5, 8.5, 11.5], "rotation": 270, "texture": "#0"}}}, {"from": [6.40771, -2.03147, 7], "to": [8.40771, 1.00676, 9], "rotation": {"angle": 22.5, "axis": "z", "origin": [9.25, 1, 8]}, "faces": {"north": {"uv": [4, 9, 5, 10.51912], "texture": "#0"}, "east": {"uv": [4, 9, 5, 10.51912], "texture": "#0"}, "south": {"uv": [4, 9, 5, 10.51912], "texture": "#0"}, "west": {"uv": [4, 9, 5, 10.51912], "texture": "#0"}}}, {"from": [5, 2, 7], "to": [11, 4, 9], "rotation": {"angle": 0, "axis": "y", "origin": [0, 3, 7]}, "faces": {"north": {"uv": [4.5, 6.5, 7.5, 7.5], "texture": "#0"}, "east": {"uv": [4, 11, 5, 12], "texture": "#0"}, "south": {"uv": [7, 4, 10, 5], "texture": "#0"}, "up": {"uv": [13.5, 6, 10.5, 5], "texture": "#0"}, "down": {"uv": [13.5, 6, 10.5, 7], "texture": "#0"}}}, {"from": [5.5, -1, 8], "to": [7.25, 1.75, 8], "rotation": {"angle": -45, "axis": "z", "origin": [7, 2, 8]}, "faces": {"north": {"uv": [11.5, 7, 13, 9], "texture": "#0"}, "south": {"uv": [13, 7, 14.5, 9], "texture": "#0"}}}, {"from": [3.78896, 5.54508, 8], "to": [12.78896, 17.54508, 8], "rotation": {"angle": 0, "axis": "y", "origin": [12.08896, 7.69508, 13.5]}, "faces": {"north": {"uv": [11, 9, 15.5, 15], "texture": "#0"}, "south": {"uv": [15.5, 9, 11, 15], "texture": "#0"}}}, {"from": [2, 2, 6.98], "to": [5, 5, 9.02], "rotation": {"angle": -45, "axis": "z", "origin": [5, 2, 7]}, "faces": {"north": {"uv": [0.1, 10.5, 1.54, 12], "texture": "#0"}, "south": {"uv": [11.46, 3.5, 12.9, 5], "texture": "#0"}, "west": {"uv": [2.5, 10.5, 3.5, 12], "texture": "#0"}, "up": {"uv": [12.54, 1, 11.1, 0], "texture": "#0"}, "down": {"uv": [12.54, 1, 11.1, 2], "texture": "#0"}}}, {"from": [6, 3.5, 7.5], "to": [10, 9.5, 8.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [6, 4, 7.5]}, "faces": {"north": {"uv": [1.5, 7.5, 3.5, 10.5], "texture": "#0"}, "east": {"uv": [0.5, 7.5, 0, 10.5], "texture": "#0"}, "south": {"uv": [3.5, 7.5, 1.5, 10.5], "texture": "#0"}, "west": {"uv": [4, 7.5, 3.5, 10.5], "texture": "#0"}, "up": {"uv": [3.5, 0, 1.5, 0.5], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, 90, 0], "translation": [0, 6.75, 0], "scale": [0.9, 0.9, 0.9]}, "thirdperson_lefthand": {"rotation": [0, -90, 0], "translation": [0, 6.75, 0], "scale": [0.9, 0.9, 0.9]}, "firstperson_righthand": {"rotation": [-9, 90, 0], "translation": [2, 5.75, -0.25], "scale": [0.7, 0.7, 0.7]}, "firstperson_lefthand": {"rotation": [-9, -90, 0], "translation": [2, 5.75, -0.25], "scale": [0.7, 0.7, 0.7]}, "ground": {"rotation": [-90, 0, 90], "translation": [-2.25, 0, -0.25], "scale": [0.7, 0.7, 0.7]}, "gui": {"rotation": [32, -34, 45], "translation": [-2, 1.5, 0], "scale": [0.75, 0.75, 0.75]}, "head": {"rotation": [0, 0, -117], "translation": [-11, 5, 0]}, "fixed": {"translation": [0, 4, -0.5], "scale": [1.2, 1.2, 1.2]}}}