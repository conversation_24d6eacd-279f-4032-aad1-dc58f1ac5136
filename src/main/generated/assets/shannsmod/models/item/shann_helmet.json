{"format_version": "1.9.0", "credit": "Made with Blockbench", "texture_size": [64, 64], "textures": {"0": "shannsmod:item/shann_helmet", "particle": "shannsmod:item/shann_helmet"}, "elements": [{"name": "Head", "from": [4, 3.5, 4], "to": [12, 10.25, 12], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 3.25, 12]}, "faces": {"north": {"uv": [2, 2, 4, 3.6875], "texture": "#0"}, "east": {"uv": [0, 2, 2, 3.6875], "texture": "#0"}, "south": {"uv": [6, 2, 8, 3.6875], "texture": "#0"}, "west": {"uv": [4, 2, 6, 3.6875], "texture": "#0"}, "up": {"uv": [4, 2, 2, 0], "texture": "#0"}, "down": {"uv": [8, 4, 6, 6], "texture": "#0"}}}, {"name": "Head", "from": [3.5, 3.75, 3.5], "to": [12.5, 10.5, 12.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 3.25, 12]}, "faces": {"north": {"uv": [10, 2, 12, 3.5625], "texture": "#0"}, "east": {"uv": [8, 2, 10, 3.5625], "texture": "#0"}, "south": {"uv": [14, 2, 16, 3.5625], "texture": "#0"}, "west": {"uv": [12, 2, 14, 3.5625], "texture": "#0"}, "up": {"uv": [12, 2, 10, 0], "texture": "#0"}, "down": {"uv": [2, 4, 0, 6], "texture": "#0"}}}, {"name": "Head", "from": [2.5, 9.5, 6.5], "to": [5.5, 12.5, 8.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 3.25, 12]}, "faces": {"north": {"uv": [9, 5, 9.75, 5.75], "texture": "#0"}, "east": {"uv": [8, 5, 8.5, 5.75], "texture": "#0"}, "south": {"uv": [9.75, 5, 10.5, 5.75], "texture": "#0"}, "west": {"uv": [8.5, 5, 9, 5.75], "texture": "#0"}, "up": {"uv": [8.75, 5, 8, 4.5], "texture": "#0"}, "down": {"uv": [9.5, 4.5, 8.75, 5], "texture": "#0"}}}, {"name": "Head", "from": [10.5, 9.5, 6.5], "to": [13.5, 12.5, 8.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 3.25, 12]}, "faces": {"north": {"uv": [12.25, 5, 11.5, 5.75], "texture": "#0"}, "east": {"uv": [11.5, 5, 11, 5.75], "texture": "#0"}, "south": {"uv": [13, 5, 12.25, 5.75], "texture": "#0"}, "west": {"uv": [11, 5, 10.5, 5.75], "texture": "#0"}, "up": {"uv": [10.5, 5, 11.25, 4.5], "texture": "#0"}, "down": {"uv": [11.25, 4.5, 12, 5], "texture": "#0"}}}, {"name": "Head", "from": [4.75, 3, 3.5], "to": [11.25, 3.75, 3.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 3.25, 12]}, "faces": {"north": {"uv": [8, 4.25, 10.25, 4.5], "texture": "#0"}, "south": {"uv": [10.25, 4.25, 12.5, 4.5], "texture": "#0"}}}, {"name": "Head", "from": [3.5, 1.75, 3.5], "to": [12.5, 4, 12.5], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 3.5, 12]}, "faces": {"north": {"uv": [10, 3.5625, 12, 4], "texture": "#0"}, "east": {"uv": [8, 3.5625, 10, 4], "texture": "#0"}, "south": {"uv": [14, 3.5625, 16, 4], "texture": "#0"}, "west": {"uv": [12, 3.5625, 14, 4], "texture": "#0"}, "up": {"uv": [4, 6, 2, 4], "texture": "#0"}, "down": {"uv": [14, 0, 12, 2], "texture": "#0"}}}, {"name": "Head", "from": [4, 2.25, 4], "to": [12, 3.5, 12], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 3.5, 12]}, "faces": {"north": {"uv": [2, 3.6875, 4, 4], "texture": "#0"}, "east": {"uv": [0, 3.6875, 2, 4], "texture": "#0"}, "south": {"uv": [6, 3.6875, 8, 4], "texture": "#0"}, "west": {"uv": [4, 3.6875, 6, 4], "texture": "#0"}, "up": {"uv": [6, 6, 4, 4], "texture": "#0"}, "down": {"uv": [6, 0, 4, 2], "texture": "#0"}}}, {"name": "Head", "from": [6, 3.5, 4], "to": [10, 4.25, 4], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 3.5, 12]}, "faces": {"north": {"uv": [8, 4, 9, 4.25], "texture": "#0"}, "south": {"uv": [9, 4, 10, 4.25], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [0, 45, 0], "scale": [0.55, 0.55, 0.55]}, "thirdperson_lefthand": {"rotation": [0, 45, 0], "scale": [0.55, 0.55, 0.55]}, "firstperson_righthand": {"rotation": [0, 45, 0], "translation": [1.25, 4.75, 0], "scale": [0.55, 0.55, 0.55]}, "firstperson_lefthand": {"rotation": [0, 45, 0], "translation": [1.25, 4.75, 0], "scale": [0.55, 0.55, 0.55]}, "ground": {"rotation": [90, 180, 0], "translation": [0, 4.5, 6.5], "scale": [1.1, 0.9, 1.15]}, "gui": {"rotation": [-122, 45, 135], "translation": [-2.25, 1.5, 0], "scale": [0.9, 0.9, 0.9]}, "head": {"rotation": [-2, 0, 0], "translation": [0, 7.25, -0.75], "scale": [2, 1.9, 2]}, "fixed": {"translation": [0, 3, -6.5], "scale": [1.7, 1.5, 1.7]}}, "groups": [{"name": "up", "origin": [8, -19.75, 8], "color": 0, "children": [0, 1, 2, 3, 4]}, {"name": "down", "origin": [8, -19.75, 8], "color": 0, "children": [5, 6, 7]}]}