{"values": ["shannsmod:arcane_helmet", "shannsmod:arcane_chestplate", "shannsmod:arcane_leggings", "shannsmod:arcane_boots", "shan<PERSON><PERSON>d:celebration_helmet", "shannsmod:celebration_chestplate", "shannsmod:celebration_leggings", "shan<PERSON>mod:celebration_boots", "shannsmod:spring_helmet", "shannsmod:spring_chestplate", "shannsmod:spring_leggings", "shannsmod:spring_boots", "shan<PERSON><PERSON><PERSON>:summer_helmet", "shannsmod:summer_chestplate", "shannsmod:summer_leggings", "shan<PERSON><PERSON><PERSON>:summer_boots", "shanns<PERSON>d:autumn_helmet", "shannsmod:autumn_chestplate", "shannsmod:autumn_leggings", "shan<PERSON><PERSON>d:autumn_boots", "shan<PERSON><PERSON>d:winter_helmet", "shannsmod:winter_chestplate", "shannsmod:winter_leggings", "shan<PERSON><PERSON>d:winter_boots", "shan<PERSON><PERSON>d:clown_helmet", "shannsmod:bone_king_helmet", "shannsmod:bone_king_chestplate", "shan<PERSON><PERSON>d:bone_king_leggings", "shanns<PERSON>d:bone_king_boots", "s<PERSON><PERSON><PERSON><PERSON>:demon_lord_helmet", "shan<PERSON><PERSON><PERSON>:demon_lord_chestplate", "s<PERSON><PERSON><PERSON><PERSON>:demon_lord_leggings", "shan<PERSON><PERSON><PERSON>:demon_lord_boots", "shannsmod:halloween_pumpkin_helmet", "shannsmod:straw_hat", "s<PERSON><PERSON><PERSON><PERSON>:shadow_assassin_helmet", "shan<PERSON><PERSON><PERSON>:shadow_assassin_chestplate", "s<PERSON><PERSON><PERSON><PERSON>:shadow_assassin_leggings", "s<PERSON><PERSON><PERSON><PERSON>:shadow_assassin_boots", "shannsmod:shann_helmet"]}